/*
 * YAFFS: Yet Another Flash File System. A NAND-flash specific file system.
 * YAFFS: 又一个Flash文件系统。专为NAND闪存设计的文件系统。
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 * 版权所有 (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 * 作者：<PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 * 本程序是自由软件；您可以根据自由软件基金会发布的GNU通用公共许可证第2版
 * 的条款重新分发和/或修改它。
 */

#include "yportenv.h"        /* 平台环境相关头文件 */
#include "yaffs_trace.h"     /* YAFFS跟踪调试头文件 */

#include "yaffs_guts.h"      /* YAFFS核心内部结构和函数 */

#include "yaffs_cache.h"     /* 缓存管理 */
#include "yaffs_endian.h"    /* 字节序处理 */
#include "yaffs_getblockinfo.h"  /* 块信息获取 */
#include "yaffs_tagscompat.h"    /* 标签兼容性 */
#include "yaffs_tagsmarshall.h"  /* 标签序列化 */
#include "yaffs_nand.h"      /* NAND闪存操作 */
#include "yaffs_yaffs1.h"    /* YAFFS1版本支持 */
#include "yaffs_yaffs2.h"    /* YAFFS2版本支持 */
#include "yaffs_bitmap.h"    /* 位图操作 */
#include "yaffs_verify.h"    /* 验证功能 */
#include "yaffs_nand.h"      /* NAND闪存操作（重复包含） */
#include "yaffs_packedtags2.h"   /* 打包标签v2 */
#include "yaffs_nameval.h"   /* 名称-值对处理 */
#include "yaffs_allocator.h" /* 内存分配器 */
#include "yaffs_attribs.h"   /* 文件属性处理 */
#include "yaffs_summary.h"   /* 摘要信息 */

/* 注意：YAFFS_GC_GOOD_ENOUGH 必须 <= YAFFS_GC_PASSIVE_THRESHOLD */
/* 垃圾回收阈值定义 */
#define YAFFS_GC_GOOD_ENOUGH 2          /* 垃圾回收足够好的阈值 */
#define YAFFS_GC_PASSIVE_THRESHOLD 4    /* 被动垃圾回收阈值 */

#include "yaffs_ecc.h"       /* 错误校正码 */

/* 前向声明 */

static void yaffs_fix_null_name(struct yaffs_obj *obj, YCHAR *name,
				int buffer_size);

/* 计算chunk和偏移量的函数 */

void yaffs_addr_to_chunk(struct yaffs_dev *dev, loff_t addr,
				int *chunk_out, u32 *offset_out)
{
	int chunk;
	u32 offset;

	/* 通过右移获取chunk编号 */
	chunk = (u32) (addr >> dev->chunk_shift);

	if (dev->chunk_div == 1) {
		/* 简单的2的幂情况 */
		offset = (u32) (addr & dev->chunk_mask);
	} else {
		/* 非2的幂情况 */

		loff_t chunk_base;

		chunk /= dev->chunk_div;

		/* 计算chunk的基地址 */
		chunk_base = ((loff_t) chunk) * dev->data_bytes_per_chunk;
		offset = (u32) (addr - chunk_base);
	}

	/* 输出计算结果 */
	*chunk_out = chunk;
	*offset_out = offset;
}

/* 返回大于或等于给定数字的2的幂所需的位移数
 * 注意：我们不试图处理所有可能的数字，这个函数不需要极高的效率。
 */

static inline u32 calc_shifts_ceiling(u32 x)
{
	int extra_bits;  /* 额外的位数 */
	int shifts;      /* 位移数 */

	shifts = extra_bits = 0;

	/* 计算所需的位移数 */
	while (x > 1) {
		if (x & 1)
			extra_bits++;  /* 如果有额外的位，记录下来 */
		x >>= 1;
		shifts++;
	}

	/* 如果有额外的位，需要多一次位移 */
	if (extra_bits)
		shifts++;

	return shifts;
}

/* 返回使第0位为1所需的位移数
 */

static inline u32 calc_shifts(u32 x)
{
	u32 shifts;

	shifts = 0;

	if (!x)
		return 0;

	/* 计算右移多少位才能使最低位为1 */
	while (!(x & 1)) {
		x >>= 1;
		shifts++;
	}

	return shifts;
}

/*
 * 临时缓冲区操作。
 */

/* 初始化临时缓冲区 */
static int yaffs_init_tmp_buffers(struct yaffs_dev *dev)
{
	int i;
	u8 *buf = (u8 *) 1;

	/* 清零临时缓冲区数组 */
	memset(dev->temp_buffer, 0, sizeof(dev->temp_buffer));

	/* 为每个临时缓冲区分配内存 */
	for (i = 0; buf && i < YAFFS_N_TEMP_BUFFERS; i++) {
		dev->temp_buffer[i].in_use = 0;  /* 标记为未使用 */
		buf = kmalloc(dev->param.total_bytes_per_chunk, GFP_NOFS);
		dev->temp_buffer[i].buffer = buf;
	}

	return buf ? YAFFS_OK : YAFFS_FAIL;
}

/* 获取一个临时缓冲区 */
u8 *yaffs_get_temp_buffer(struct yaffs_dev * dev)
{
	int i;

	/* 增加使用中的缓冲区计数 */
	dev->temp_in_use++;
	if (dev->temp_in_use > dev->max_temp)
		dev->max_temp = dev->temp_in_use;  /* 更新最大使用数 */

	/* 查找可用的临时缓冲区 */
	for (i = 0; i < YAFFS_N_TEMP_BUFFERS; i++) {
		if (dev->temp_buffer[i].in_use == 0) {
			dev->temp_buffer[i].in_use = 1;  /* 标记为使用中 */
			return dev->temp_buffer[i].buffer;
		}
	}

	yaffs_trace(YAFFS_TRACE_BUFFERS, "Out of temp buffers");
	/*
	 * 如果执行到这里，说明我们必须分配一个非托管的缓冲区
	 * 这不是好事。
	 */

	dev->unmanaged_buffer_allocs++;  /* 增加非托管分配计数 */
	return kmalloc(dev->data_bytes_per_chunk, GFP_NOFS);

}

/* 释放yaffs_dev实例中的所有temp_buffer对象
*/
void yaffs_release_temp_buffer(struct yaffs_dev *dev, u8 *buffer)
{
	int i;

	dev->temp_in_use--;  /* 减少使用中的缓冲区计数 */

	/* 在托管缓冲区中查找要释放的缓冲区 */
	for (i = 0; i < YAFFS_N_TEMP_BUFFERS; i++) {
		if (dev->temp_buffer[i].buffer == buffer) {
			dev->temp_buffer[i].in_use = 0;  /* 标记为未使用 */
			return;
		}
	}

	if (buffer) {
		/* 假设这是一个非托管的缓冲区 */
		yaffs_trace(YAFFS_TRACE_BUFFERS,
			"Releasing unmanaged temp buffer");
		kfree(buffer);
		dev->unmanaged_buffer_deallocs++;  /* 增加非托管释放计数 */
	}

}

/*
 * 用于增强鲁棒性的函数 TODO
 *
 */

/* 处理chunk写入成功的回调函数 */
static void yaffs_handle_chunk_wr_ok(struct yaffs_dev *dev, int nand_chunk,
				     const u8 *data,
				     const struct yaffs_ext_tags *tags)
{
	(void) dev;        /* 未使用的参数 */
	(void) nand_chunk; /* 未使用的参数 */
	(void) data;       /* 未使用的参数 */
	(void) tags;       /* 未使用的参数 */
}

/* 处理chunk更新的回调函数 */
static void yaffs_handle_chunk_update(struct yaffs_dev *dev, int nand_chunk,
				      const struct yaffs_ext_tags *tags)
{
	(void) dev;        /* 未使用的参数 */
	(void) nand_chunk; /* 未使用的参数 */
	(void) tags;       /* 未使用的参数 */
}

/* 处理chunk错误 */
void yaffs_handle_chunk_error(struct yaffs_dev *dev,
			      struct yaffs_block_info *bi)
{
	if (!bi->gc_prioritise) {
		bi->gc_prioritise = 1;                    /* 设置垃圾回收优先级 */
		dev->has_pending_prioritised_gc = 1;      /* 标记有待处理的优先垃圾回收 */
		bi->chunk_error_strikes++;                /* 增加错误计数 */

		if (bi->chunk_error_strikes > 3) {
			bi->needs_retiring = 1;	/* 错误太多，需要退役该块 */
			yaffs_trace(YAFFS_TRACE_ALWAYS,
				"yaffs: Block struck out");

		}
	}
}

/* 处理chunk写入错误 */
static void yaffs_handle_chunk_wr_error(struct yaffs_dev *dev, int nand_chunk,
					int erased_ok)
{
	int flash_block = nand_chunk / dev->param.chunks_per_block;  /* 计算块号 */
	struct yaffs_block_info *bi = yaffs_get_block_info(dev, flash_block);

	yaffs_handle_chunk_error(dev, bi);  /* 处理chunk错误 */

	if (erased_ok) {
		/* 这是一个实际的写入失败，
		 * 所以标记该块需要退役。*/
		bi->needs_retiring = 1;
		yaffs_trace(YAFFS_TRACE_ERROR | YAFFS_TRACE_BAD_BLOCKS,
		  "**>> Block %d needs retiring", flash_block);
	}

	/* 删除该chunk */
	yaffs_chunk_del(dev, nand_chunk, 1, __LINE__);
	yaffs_skip_rest_of_block(dev);  /* 跳过该块的剩余部分 */
}

/*
 * 验证代码
 */

/*
 * 简单的哈希函数。需要有合理的分布
 */

static inline int yaffs_hash_fn(int n)
{
	if (n < 0)
		n = -n;  /* 取绝对值 */
	return n % YAFFS_NOBJECT_BUCKETS;  /* 返回哈希值 */
}

/*
 * 访问有用的虚假对象的函数。
 * 注意：如果设置了权限，root可能在NAND中有存在。
 */

/* 获取根目录对象 */
struct yaffs_obj *yaffs_root(struct yaffs_dev *dev)
{
	return dev->root_dir;
}

/* 获取lost+found目录对象 */
struct yaffs_obj *yaffs_lost_n_found(struct yaffs_dev *dev)
{
	return dev->lost_n_found;
}

/*
 * 已擦除NAND检查函数
 */

/* 检查缓冲区是否全为0xFF（已擦除状态） */
int yaffs_check_ff(u8 *buffer, int n_bytes)
{
	/* 糟糕的、慢速的实现 */
	while (n_bytes--) {
		if (*buffer != 0xff)
			return 0;  /* 发现非0xFF字节，未完全擦除 */
		buffer++;
	}
	return 1;  /* 全部为0xFF，已擦除 */
}

/* 检查chunk是否已擦除 */
static int yaffs_check_chunk_erased(struct yaffs_dev *dev, int nand_chunk)
{
	int retval = YAFFS_OK;
	u8 *data = yaffs_get_temp_buffer(dev);  /* 获取临时缓冲区 */
	struct yaffs_ext_tags tags;
	int result;

	/* 读取chunk数据和标签 */
	result = yaffs_rd_chunk_tags_nand(dev, nand_chunk, data, &tags);

	/* 检查读取是否成功以及ECC错误 */
	if (result == YAFFS_FAIL ||
	    tags.ecc_result > YAFFS_ECC_RESULT_NO_ERROR)
		retval = YAFFS_FAIL;

	/* 检查数据是否全为0xFF且chunk未被使用 */
	if (!yaffs_check_ff(data, dev->data_bytes_per_chunk) ||
		tags.chunk_used) {
		yaffs_trace(YAFFS_TRACE_NANDACCESS,
			"Chunk %d not erased", nand_chunk);
		retval = YAFFS_FAIL;
	}

	yaffs_release_temp_buffer(dev, data);  /* 释放临时缓冲区 */

	return retval;

}

/* 验证chunk是否正确写入 */
static int yaffs_verify_chunk_written(struct yaffs_dev *dev,
				      int nand_chunk,
				      const u8 *data,
				      struct yaffs_ext_tags *tags)
{
	int retval = YAFFS_OK;
	struct yaffs_ext_tags temp_tags;
	u8 *buffer = yaffs_get_temp_buffer(dev);  /* 获取临时缓冲区 */
	int result;

	/* 读取刚写入的chunk进行验证 */
	result = yaffs_rd_chunk_tags_nand(dev, nand_chunk, buffer, &temp_tags);

	/* 比较数据和标签是否一致 */
	if (result == YAFFS_FAIL ||
	    memcmp(buffer, data, dev->data_bytes_per_chunk) ||
	    temp_tags.obj_id != tags->obj_id ||
	    temp_tags.chunk_id != tags->chunk_id ||
	    temp_tags.n_bytes != tags->n_bytes)
		retval = YAFFS_FAIL;

	yaffs_release_temp_buffer(dev, buffer);  /* 释放临时缓冲区 */

	return retval;
}


/* 检查是否有足够的可分配空间 */
int yaffs_check_alloc_available(struct yaffs_dev *dev, int n_chunks)
{
	int reserved_chunks;
	int reserved_blocks = dev->param.n_reserved_blocks;  /* 保留块数 */
	int checkpt_blocks;

	/* 计算检查点所需的块数 */
	checkpt_blocks = yaffs_calc_checkpt_blocks_required(dev);

	/* 计算总的保留chunk数 */
	reserved_chunks =
	    (reserved_blocks + checkpt_blocks) * dev->param.chunks_per_block;

	/* 检查空闲chunk数是否足够 */
	return (dev->n_free_chunks > (reserved_chunks + n_chunks));
}

/* 查找可分配的块 */
static int yaffs_find_alloc_block(struct yaffs_dev *dev)
{
	u32 i;
	struct yaffs_block_info *bi;

	if (dev->n_erased_blocks < 1) {
		/* 糟糕，我们遇到了问题。
		 * 无法获得空间进行垃圾回收
		 */
		yaffs_trace(YAFFS_TRACE_ERROR,
		  "yaffs tragedy: no more erased blocks");

		return -1;
	}

	/* 查找一个空块 */

	for (i = dev->internal_start_block; i <= dev->internal_end_block; i++) {
		dev->alloc_block_finder++;  /* 递增块查找器 */

		/* 如果超出范围，重置到起始块 */
		if (dev->alloc_block_finder < (int)dev->internal_start_block
		    || dev->alloc_block_finder > (int)dev->internal_end_block) {
			dev->alloc_block_finder = dev->internal_start_block;
		}

		bi = yaffs_get_block_info(dev, dev->alloc_block_finder);

		/* 找到空块 */
		if (bi->block_state == YAFFS_BLOCK_STATE_EMPTY) {
			bi->block_state = YAFFS_BLOCK_STATE_ALLOCATING;  /* 标记为分配中 */
			dev->seq_number++;                               /* 递增序列号 */
			bi->seq_number = dev->seq_number;                /* 设置块序列号 */
			dev->n_erased_blocks--;                          /* 减少已擦除块数 */
			yaffs_trace(YAFFS_TRACE_ALLOCATE,
			  "Allocated block %d, seq  %d, %d left" ,
			   dev->alloc_block_finder, dev->seq_number,
			   dev->n_erased_blocks);
			return dev->alloc_block_finder;
		}
	}

	yaffs_trace(YAFFS_TRACE_ALWAYS,
		"yaffs tragedy: no more erased blocks, but there should have been %d",
		dev->n_erased_blocks);

	return -1;
}

/* 分配一个chunk */
static int yaffs_alloc_chunk(struct yaffs_dev *dev, int use_reserver,
			     struct yaffs_block_info **block_ptr)
{
	int ret_val;
	struct yaffs_block_info *bi;

	if (dev->alloc_block < 0) {
		/* 获取下一个要分配的块 */
		dev->alloc_block = yaffs_find_alloc_block(dev);
		dev->alloc_page = 0;  /* 重置页计数器 */
	}

	if (!use_reserver && !yaffs_check_alloc_available(dev, 1)) {
		/* 除非允许使用保留空间，否则没有空间 */
		return -1;
	}

	/* 如果已擦除块数少于保留块数且在块的第一页，记录日志 */
	if (dev->n_erased_blocks < (int)dev->param.n_reserved_blocks
	    && dev->alloc_page == 0)
		yaffs_trace(YAFFS_TRACE_ALLOCATE, "Allocating reserve");

	/* 请给我下一页.... */
	if (dev->alloc_block >= 0) {
		bi = yaffs_get_block_info(dev, dev->alloc_block);

		/* 计算chunk编号 */
		ret_val = (dev->alloc_block * dev->param.chunks_per_block) +
		    dev->alloc_page;
		bi->pages_in_use++;  /* 增加使用中的页数 */
		yaffs_set_chunk_bit(dev, dev->alloc_block, dev->alloc_page);  /* 设置chunk位 */

		dev->alloc_page++;   /* 移动到下一页 */

		dev->n_free_chunks--;  /* 减少空闲chunk数 */

		/* 如果块已满，设置状态为满 */
		if (dev->alloc_page >= dev->param.chunks_per_block) {
			bi->block_state = YAFFS_BLOCK_STATE_FULL;
			dev->alloc_block = -1;  /* 重置分配块 */
		}

		if (block_ptr)
			*block_ptr = bi;  /* 返回块指针 */

		return ret_val;
	}

	yaffs_trace(YAFFS_TRACE_ERROR,
		"!!!!!!!!! Allocator out !!!!!!!!!!!!!!!!!");

	return -1;
}

/* 获取已擦除的chunk数量 */
static int yaffs_get_erased_chunks(struct yaffs_dev *dev)
{
	int n;

	/* 计算所有已擦除块的chunk数 */
	n = dev->n_erased_blocks * dev->param.chunks_per_block;

	/* 如果当前有分配块，加上该块剩余的chunk数 */
	if (dev->alloc_block > 0)
		n += (dev->param.chunks_per_block - dev->alloc_page);

	return n;

}

/*
 * yaffs_skip_rest_of_block() 跳过分配块的剩余部分
 * 如果我们不想写入它。
 */
void yaffs_skip_rest_of_block(struct yaffs_dev *dev)
{
	struct yaffs_block_info *bi;

	if (dev->alloc_block > 0) {
		bi = yaffs_get_block_info(dev, dev->alloc_block);
		/* 如果块正在分配中，标记为满并重置分配块 */
		if (bi->block_state == YAFFS_BLOCK_STATE_ALLOCATING) {
			bi->block_state = YAFFS_BLOCK_STATE_FULL;
			dev->alloc_block = -1;
		}
	}
}

/* 写入新的chunk */
static int yaffs_write_new_chunk(struct yaffs_dev *dev,
				 const u8 *data,
				 struct yaffs_ext_tags *tags, int use_reserver)
{
	u32 attempts = 0;    /* 尝试次数 */
	int write_ok = 0;    /* 写入是否成功 */
	int chunk;

	yaffs2_checkpt_invalidate(dev);  /* 使检查点无效 */

	do {
		struct yaffs_block_info *bi = 0;
		int erased_ok = 0;  /* 擦除检查是否通过 */

		/* 分配一个chunk */
		chunk = yaffs_alloc_chunk(dev, use_reserver, &bi);
		if (chunk < 0) {
			/* 没有空间 */
			break;
		}

		/* 首先检查这个chunk是否已擦除，如果需要检查的话。
		 * 检查策略（除非强制总是开启）如下：
		 *
		 * 检查我们尝试在块中写入的第一页。
		 * 如果检查通过，那么我们不需要检查更多。
		 * 如果检查失败，我们再次检查...
		 * 如果块已被擦除，我们不需要检查。
		 *
		 * 但是，如果块已被优先用于垃圾回收，
		 * 那么我们认为这个块可能有些奇怪，停止使用它。
		 *
		 * 理由：我们应该只会看到由于断电导致部分写入的chunk
		 * 没有被擦除。这个检查策略应该用很少的检查就能捕获
		 * 这种情况，从而节省大量很可能不需要的检查。
		 *
		 * 对上述的修改：
		 * 如果擦除检查失败或写入失败，我们跳过块的剩余部分。
		 */

		/* 让我们试一试 */
		attempts++;

		/* 如果总是检查擦除，重置跳过标志 */
		if (dev->param.always_check_erased)
			bi->skip_erased_check = 0;

		/* 如果不跳过擦除检查 */
		if (!bi->skip_erased_check) {
			erased_ok = yaffs_check_chunk_erased(dev, chunk);
			if (erased_ok != YAFFS_OK) {
				yaffs_trace(YAFFS_TRACE_ERROR,
				  "**>> yaffs chunk %d was not erased",
				  chunk);

				/* 如果未擦除，删除这个chunk，
				 * 跳过块的剩余部分并尝试另一个chunk */
				yaffs_chunk_del(dev, chunk, 1, __LINE__);
				yaffs_skip_rest_of_block(dev);
				continue;
			}
		}

		/* 写入chunk数据和标签 */
		write_ok = yaffs_wr_chunk_tags_nand(dev, chunk, data, tags);

		/* 如果不跳过擦除检查，验证写入的数据 */
		if (!bi->skip_erased_check)
			write_ok =
			    yaffs_verify_chunk_written(dev, chunk, data, tags);

		if (write_ok != YAFFS_OK) {
			/* 清理中止的写入，跳到下一个块并尝试另一个chunk */
			yaffs_handle_chunk_wr_error(dev, chunk, erased_ok);
			continue;
		}

		bi->skip_erased_check = 1;  /* 设置跳过擦除检查标志 */

		/* 将数据复制到鲁棒性缓冲区 */
		yaffs_handle_chunk_wr_ok(dev, chunk, data, tags);

	} while (write_ok != YAFFS_OK &&
		 (yaffs_wr_attempts == 0 || attempts <= yaffs_wr_attempts));

	if (!write_ok)
		chunk = -1;  /* 写入失败，返回-1 */

	/* 如果尝试了多次，记录重试次数 */
	if (attempts > 1) {
		yaffs_trace(YAFFS_TRACE_ERROR,
			"**>> yaffs write required %d attempts",
			attempts);
		dev->n_retried_writes += (attempts - 1);  /* 增加重试写入计数 */
	}

	return chunk;  /* 返回chunk编号或-1 */
}

/*
 * 块退役，用于处理损坏的块。
 */

static void yaffs_retire_block(struct yaffs_dev *dev, int flash_block)
{
	struct yaffs_block_info *bi = yaffs_get_block_info(dev, flash_block);

	yaffs2_checkpt_invalidate(dev);  /* 使检查点无效 */

	yaffs2_clear_oldest_dirty_seq(dev, bi);  /* 清除最旧的脏序列 */

	/* 尝试标记为坏块 */
	if (yaffs_mark_bad(dev, flash_block) != YAFFS_OK) {
		/* 如果标记失败，尝试擦除块 */
		if (yaffs_erase_block(dev, flash_block) != YAFFS_OK) {
			yaffs_trace(YAFFS_TRACE_ALWAYS,
				"yaffs: Failed to mark bad and erase block %d",
				flash_block);
		} else {
			struct yaffs_ext_tags tags;
			int chunk_id =
			    flash_block * dev->param.chunks_per_block;

			u8 *buffer = yaffs_get_temp_buffer(dev);  /* 获取临时缓冲区 */

			/* 准备坏块标记 */
			memset(buffer, 0xff, dev->data_bytes_per_chunk);
			memset(&tags, 0, sizeof(tags));
			tags.seq_number = YAFFS_SEQUENCE_BAD_BLOCK;  /* 设置坏块序列号 */

			/* 写入坏块标记 */
			if (dev->tagger.write_chunk_tags_fn(dev, chunk_id -
							dev->chunk_offset,
							buffer,
							&tags) != YAFFS_OK)
				yaffs_trace(YAFFS_TRACE_ALWAYS,
					"yaffs: Failed to write bad block marker to block %d",
					flash_block);

			yaffs_release_temp_buffer(dev, buffer);  /* 释放临时缓冲区 */
		}
	}

	/* 设置块状态为死亡 */
	bi->block_state = YAFFS_BLOCK_STATE_DEAD;
	bi->gc_prioritise = 0;      /* 清除垃圾回收优先级 */
	bi->needs_retiring = 0;     /* 清除需要退役标志 */

	dev->n_retired_blocks++;    /* 增加退役块计数 */
}

/*---------------- 名称处理函数 ------------*/

/* 从对象头加载名称 */
static void yaffs_load_name_from_oh(struct yaffs_dev *dev, YCHAR *name,
				    const YCHAR *oh_name, int buff_size)
{
#ifdef CONFIG_YAFFS_AUTO_UNICODE
	if (dev->param.auto_unicode) {
		if (*oh_name) {
			/* 这是一个ASCII名称，进行ASCII到unicode的转换 */
			const char *ascii_oh_name = (const char *)oh_name;
			int n = buff_size - 1;
			while (n > 0 && *ascii_oh_name) {
				*name = *ascii_oh_name;
				name++;
				ascii_oh_name++;
				n--;
			}
		} else {
			/* Unicode名称，从第二个字符开始复制 */
			strncpy(name, oh_name + 1, buff_size - 1);
		}
	} else {
#else
	(void) dev;  /* 未使用的参数 */
	{
#endif
		strncpy(name, oh_name, buff_size - 1);
	}
}

/* 从名称加载到对象头 */
static void yaffs_load_oh_from_name(struct yaffs_dev *dev, YCHAR *oh_name,
				    const YCHAR *name)
{
#ifdef CONFIG_YAFFS_AUTO_UNICODE

	int is_ascii;
	const YCHAR *w;

	if (dev->param.auto_unicode) {

		is_ascii = 1;
		w = name;

		/* 判断名称是否适合ASCII字符集 */
		while (is_ascii && *w) {
			if ((*w) & 0xff00)
				is_ascii = 0;  /* 发现非ASCII字符 */
			w++;
		}

		if (is_ascii) {
			/* 这是一个ASCII名称，所以将unicode转换为ascii */
			char *ascii_oh_name = (char *)oh_name;
			int n = YAFFS_MAX_NAME_LENGTH - 1;
			while (n > 0 && *name) {
				*ascii_oh_name = *name;
				name++;
				ascii_oh_name++;
				n--;
			}
		} else {
			/* Unicode名称，从第二个YCHAR开始保存 */
			*oh_name = 0;
			strncpy(oh_name + 1, name, YAFFS_MAX_NAME_LENGTH - 2);
		}
	} else {
#else
	dev = dev;  /* 避免编译器警告 */
	{
#endif
		strncpy(oh_name, name, YAFFS_MAX_NAME_LENGTH - 1);
	}
}

/* 计算名称的校验和 */
static u16 yaffs_calc_name_sum(const YCHAR *name)
{
	u16 sum = 0;
	u16 i = 1;

	if (!name)
		return 0;

	while ((*name) && i < (YAFFS_MAX_NAME_LENGTH / 2)) {

		/* 0x1f掩码是大小写不敏感的 */
		sum += ((*name) & 0x1f) * i;
		i++;
		name++;
	}
	return sum;
}


/* 设置对象名称 */
void yaffs_set_obj_name(struct yaffs_obj *obj, const YCHAR * name)
{
	memset(obj->short_name, 0, sizeof(obj->short_name));  /* 清零短名称 */

	if (name && !name[0]) {
		/* 如果名称为空，修复空名称 */
		yaffs_fix_null_name(obj, obj->short_name,
				YAFFS_SHORT_NAME_LENGTH);
		name = obj->short_name;
	} else if (name &&
		strnlen(name, YAFFS_SHORT_NAME_LENGTH + 1) <=
		YAFFS_SHORT_NAME_LENGTH)  {
		/* 如果名称长度合适，复制到短名称 */
		strcpy(obj->short_name, name);
	}

	obj->sum = yaffs_calc_name_sum(name);  /* 计算名称校验和 */
}

/* 从对象头设置对象名称 */
void yaffs_set_obj_name_from_oh(struct yaffs_obj *obj,
				const struct yaffs_obj_hdr *oh)
{
#ifdef CONFIG_YAFFS_AUTO_UNICODE
	YCHAR tmp_name[YAFFS_MAX_NAME_LENGTH + 1];
	memset(tmp_name, 0, sizeof(tmp_name));
	yaffs_load_name_from_oh(obj->my_dev, tmp_name, oh->name,
				YAFFS_MAX_NAME_LENGTH + 1);
	yaffs_set_obj_name(obj, tmp_name);
#else
	yaffs_set_obj_name(obj, oh->name);
#endif
}

/* 获取最大文件大小 */
loff_t yaffs_max_file_size(struct yaffs_dev *dev)
{
	if (sizeof(loff_t) < 8)
		return YAFFS_MAX_FILE_SIZE_32;  /* 32位系统的最大文件大小 */
	else
		return ((loff_t) YAFFS_MAX_CHUNK_ID) * dev->data_bytes_per_chunk;
}

/*-------------------- TNODES -------------------

 * 备用tnode列表
 * 列表使用tnode中的第一个指针连接在一起。
 */

/* 获取一个tnode */
struct yaffs_tnode *yaffs_get_tnode(struct yaffs_dev *dev)
{
	struct yaffs_tnode *tn = yaffs_alloc_raw_tnode(dev);

	if (tn) {
		memset(tn, 0, dev->tnode_size);  /* 清零tnode */
		dev->n_tnodes++;                 /* 增加tnode计数 */
	}

	dev->checkpoint_blocks_required = 0;	/* 强制重新计算 */

	return tn;
}

/* FreeTnode释放一个tnode并将其放回空闲列表 */
static void yaffs_free_tnode(struct yaffs_dev *dev, struct yaffs_tnode *tn)
{
	yaffs_free_raw_tnode(dev, tn);
	dev->n_tnodes--;                        /* 减少tnode计数 */
	dev->checkpoint_blocks_required = 0;	/* 强制重新计算 */
}

/* 反初始化tnodes和对象 */
static void yaffs_deinit_tnodes_and_objs(struct yaffs_dev *dev)
{
	yaffs_deinit_raw_tnodes_and_objs(dev);
	dev->n_obj = 0;      /* 重置对象计数 */
	dev->n_tnodes = 0;   /* 重置tnode计数 */
}

/* 加载tnode第0级的值 */
static void yaffs_load_tnode_0(struct yaffs_dev *dev, struct yaffs_tnode *tn,
			unsigned pos, unsigned val)
{
	u32 *map = (u32 *) tn;  /* 将tnode转换为32位整数数组 */
	u32 bit_in_map;         /* 在映射中的位位置 */
	u32 bit_in_word;        /* 在字中的位位置 */
	u32 word_in_map;        /* 在映射中的字位置 */
	u32 mask;               /* 位掩码 */

	pos &= YAFFS_TNODES_LEVEL0_MASK;  /* 限制位置范围 */
	val >>= dev->chunk_grp_bits;      /* 右移chunk组位数 */

	/* 计算位位置 */
	bit_in_map = pos * dev->tnode_width;
	word_in_map = bit_in_map / 32;
	bit_in_word = bit_in_map & (32 - 1);

	/* 创建掩码并设置值 */
	mask = dev->tnode_mask << bit_in_word;

	map[word_in_map] &= ~mask;  /* 清除原有位 */
	map[word_in_map] |= (mask & (val << bit_in_word));  /* 设置新值 */

	/* 如果值跨越两个字，处理第二个字 */
	if (dev->tnode_width > (32 - bit_in_word)) {
		bit_in_word = (32 - bit_in_word);
		word_in_map++;
		mask = dev->tnode_mask >> bit_in_word;
		map[word_in_map] &= ~mask;
		map[word_in_map] |= (mask & (val >> bit_in_word));
	}
}

/* 获取组基址 */
u32 yaffs_get_group_base(struct yaffs_dev *dev, struct yaffs_tnode *tn,
			 unsigned pos)
{
	u32 *map = (u32 *) tn;  /* 将tnode转换为32位整数数组 */
	u32 bit_in_map;         /* 在映射中的位位置 */
	u32 bit_in_word;        /* 在字中的位位置 */
	u32 word_in_map;        /* 在映射中的字位置 */
	u32 val;                /* 返回值 */

	pos &= YAFFS_TNODES_LEVEL0_MASK;  /* 限制位置范围 */

	/* 计算位位置 */
	bit_in_map = pos * dev->tnode_width;
	word_in_map = bit_in_map / 32;
	bit_in_word = bit_in_map & (32 - 1);

	/* 提取值 */
	val = map[word_in_map] >> bit_in_word;

	/* 如果值跨越两个字，合并第二个字的部分 */
	if (dev->tnode_width > (32 - bit_in_word)) {
		bit_in_word = (32 - bit_in_word);
		word_in_map++;
		val |= (map[word_in_map] << bit_in_word);
	}

	val &= dev->tnode_mask;        /* 应用tnode掩码 */
	val <<= dev->chunk_grp_bits;   /* 左移chunk组位数 */

	return val;
}

/* ------------------- 单个tnode操作结束 -----------------*/

/* ---------操作查找树（由tnodes组成）的函数 ------
 * 查找树由顶层tnode和树中的top_level数量表示。
 * 0表示树中只有第0级tnode。
 */

/* FindLevel0Tnode查找第0级tnode，如果存在的话。 */
struct yaffs_tnode *yaffs_find_tnode_0(struct yaffs_dev *dev,
				       struct yaffs_file_var *file_struct,
				       u32 chunk_id)
{
	struct yaffs_tnode *tn = file_struct->top;  /* 从顶层tnode开始 */
	u32 i;
	int required_depth;  /* 所需深度 */
	int level = file_struct->top_level;  /* 当前级别 */

	(void) dev;  /* 未使用的参数 */

	/* 检查合理的级别和chunk ID */
	if (level < 0 || level > YAFFS_TNODES_MAX_LEVEL)
		return NULL;

	if (chunk_id > YAFFS_MAX_CHUNK_ID)
		return NULL;

	/* 首先检查我们是否足够高（即足够的top_level） */

	i = chunk_id >> YAFFS_TNODES_LEVEL0_BITS;
	required_depth = 0;
	while (i) {
		i >>= YAFFS_TNODES_INTERNAL_BITS;
		required_depth++;  /* 计算所需深度 */
	}

	if (required_depth > file_struct->top_level)
		return NULL;	/* 不够高，所以找不到 */

	/* 向下遍历到第0级 */
	while (level > 0 && tn) {
		tn = tn->internal[(chunk_id >>
				   (YAFFS_TNODES_LEVEL0_BITS +
				    (level - 1) *
				    YAFFS_TNODES_INTERNAL_BITS)) &
				  YAFFS_TNODES_INTERNAL_MASK];
		level--;  /* 降低级别 */
	}

	return tn;
}

/* add_find_tnode_0查找第0级tnode（如果存在），
 * 否则首先扩展树。
 * 这分两步进行：
 *  1. 如果树不够高，则使其更高。
 *  2. 向下扫描树，朝向第0级tnode，根据需要添加tnodes。
 *
 * 在修改树时使用。
 *
 * 如果tn参数为NULL，则将添加一个新的tnode，否则
 * 指定的tn将被插入到树中。
 */

struct yaffs_tnode *yaffs_add_find_tnode_0(struct yaffs_dev *dev,
					   struct yaffs_file_var *file_struct,
					   u32 chunk_id,
					   struct yaffs_tnode *passed_tn)
{
	int required_depth;  /* 所需深度 */
	int i;
	int l;
	struct yaffs_tnode *tn;
	u32 x;

	/* 检查合理的级别和页ID */
	if (file_struct->top_level < 0 ||
	    file_struct->top_level > YAFFS_TNODES_MAX_LEVEL)
		return NULL;

	if (chunk_id > YAFFS_MAX_CHUNK_ID)
		return NULL;

	/* 首先检查我们是否足够高（即足够的top_level） */

	x = chunk_id >> YAFFS_TNODES_LEVEL0_BITS;
	required_depth = 0;
	while (x) {
		x >>= YAFFS_TNODES_INTERNAL_BITS;
		required_depth++;  /* 计算所需深度 */
	}

	if (required_depth > file_struct->top_level) {
		/* 不够高，必须使树更高 */
		for (i = file_struct->top_level; i < required_depth; i++) {

			tn = yaffs_get_tnode(dev);  /* 获取新的tnode */

			if (tn) {
				tn->internal[0] = file_struct->top;  /* 将当前顶层作为子节点 */
				file_struct->top = tn;               /* 设置新的顶层 */
				file_struct->top_level++;           /* 增加级别 */
			} else {
				yaffs_trace(YAFFS_TRACE_ERROR,
					"yaffs: no more tnodes");
				return NULL;
			}
		}
	}

	/* 向下遍历到第0级，添加我们需要的任何东西 */

	l = file_struct->top_level;
	tn = file_struct->top;

	if (l > 0) {
		while (l > 0 && tn) {
			x = (chunk_id >>
			     (YAFFS_TNODES_LEVEL0_BITS +
			      (l - 1) * YAFFS_TNODES_INTERNAL_BITS)) &
			    YAFFS_TNODES_INTERNAL_MASK;

			if ((l > 1) && !tn->internal[x]) {
				/* 添加缺失的非零级tnode */
				tn->internal[x] = yaffs_get_tnode(dev);
				if (!tn->internal[x])
					return NULL;
			} else if (l == 1) {
				/* 从第1级查看第0级 */
				if (passed_tn) {
					/* 如果我们已经有一个，释放它 */
					if (tn->internal[x])
						yaffs_free_tnode(dev,
							tn->internal[x]);
					tn->internal[x] = passed_tn;  /* 使用传入的tnode */

				} else if (!tn->internal[x]) {
					/* 没有一个，也没有传入 */
					tn->internal[x] = yaffs_get_tnode(dev);
					if (!tn->internal[x])
						return NULL;
				}
			}

			tn = tn->internal[x];  /* 移动到下一级 */
			l--;
		}
	} else {
		/* 顶层就是第0级 */
		if (passed_tn) {
			memcpy(tn, passed_tn,
			       (dev->tnode_width * YAFFS_NTNODES_LEVEL0) / 8);
			yaffs_free_tnode(dev, passed_tn);  /* 释放传入的tnode */
		}
	}

	return tn;
}

/* 检查标签是否匹配 */
static int yaffs_tags_match(const struct yaffs_ext_tags *tags, int obj_id,
			    int chunk_obj)
{
	return (tags->chunk_id == (u32)chunk_obj &&
		tags->obj_id == (u32)obj_id &&
		!tags->is_deleted) ? 1 : 0;  /* 匹配返回1，否则返回0 */

}

/* 在组中查找chunk */
static int yaffs_find_chunk_in_group(struct yaffs_dev *dev, int the_chunk,
					struct yaffs_ext_tags *tags, int obj_id,
					int inode_chunk)
{
	int j;

	/* 遍历chunk组 */
	for (j = 0; the_chunk && j < dev->chunk_grp_size; j++) {
		/* 检查chunk位是否设置 */
		if (yaffs_check_chunk_bit
		    (dev, the_chunk / dev->param.chunks_per_block,
		     the_chunk % dev->param.chunks_per_block)) {

			if (dev->chunk_grp_size == 1)
				return the_chunk;  /* 组大小为1，直接返回 */
			else {
				/* 读取chunk标签进行匹配 */
				yaffs_rd_chunk_tags_nand(dev, the_chunk, NULL,
							 tags);
				if (yaffs_tags_match(tags,
							obj_id, inode_chunk)) {
					/* 找到了 */
					return the_chunk;
				}
			}
		}
		the_chunk++;  /* 检查下一个chunk */
	}
	return -1;  /* 未找到 */
}

/* 在文件中查找chunk */
int yaffs_find_chunk_in_file(struct yaffs_obj *in, int inode_chunk,
				    struct yaffs_ext_tags *tags)
{
	/* 获取Tnode，然后获取第0级偏移chunk偏移 */
	struct yaffs_tnode *tn;
	int the_chunk = -1;
	struct yaffs_ext_tags local_tags;
	int ret_val = -1;
	struct yaffs_dev *dev = in->my_dev;

	if (!tags) {
		/* 传入了NULL，所以使用我们自己的标签空间 */
		tags = &local_tags;
	}

	/* 查找第0级tnode */
	tn = yaffs_find_tnode_0(dev, &in->variant.file_variant, inode_chunk);

	if (!tn)
		return ret_val;  /* 未找到tnode */

	/* 获取组基址 */
	the_chunk = yaffs_get_group_base(dev, tn, inode_chunk);

	/* 在组中查找chunk */
	ret_val = yaffs_find_chunk_in_group(dev, the_chunk, tags, in->obj_id,
					      inode_chunk);
	return ret_val;
}

/* 查找并删除文件chunk */
static int yaffs_find_del_file_chunk(struct yaffs_obj *in, int inode_chunk,
				     struct yaffs_ext_tags *tags)
{
	/* 获取Tnode，然后获取第0级偏移chunk偏移 */
	struct yaffs_tnode *tn;
	int the_chunk = -1;
	struct yaffs_ext_tags local_tags;
	struct yaffs_dev *dev = in->my_dev;
	int ret_val = -1;

	if (!tags) {
		/* 传入了NULL，所以使用我们自己的标签空间 */
		tags = &local_tags;
	}

	/* 查找第0级tnode */
	tn = yaffs_find_tnode_0(dev, &in->variant.file_variant, inode_chunk);

	if (!tn)
		return ret_val;  /* 未找到tnode */

	/* 获取组基址 */
	the_chunk = yaffs_get_group_base(dev, tn, inode_chunk);

	/* 在组中查找chunk */
	ret_val = yaffs_find_chunk_in_group(dev, the_chunk, tags, in->obj_id,
					      inode_chunk);

	/* 删除文件结构中的条目（如果找到） */
	if (ret_val != -1)
		yaffs_load_tnode_0(dev, tn, inode_chunk, 0);  /* 清零tnode条目 */

	return ret_val;
}

/* 将chunk放入文件 */
int yaffs_put_chunk_in_file(struct yaffs_obj *in, int inode_chunk,
			    int nand_chunk, int in_scan)
{
	/* 注意：in_scan除非在扫描时才非零。
	 * 对于前向扫描，in_scan > 0；
	 * 对于后向扫描，in_scan < 0
	 *
	 * nand_chunk = 0是一个虚拟插入，以确保tnodes存在。
	 */

	struct yaffs_tnode *tn;
	struct yaffs_dev *dev = in->my_dev;
	int existing_cunk;                    /* 现有chunk */
	struct yaffs_ext_tags existing_tags;  /* 现有标签 */
	struct yaffs_ext_tags new_tags;       /* 新标签 */
	unsigned existing_serial, new_serial;  /* 序列号 */

	if (in->variant_type != YAFFS_OBJECT_TYPE_FILE) {
		/* 在扫描期间忽略将chunk放入非文件的尝试。
		 * 如果不是在扫描期间，那么出了问题！
		 */
		if (!in_scan) {
			yaffs_trace(YAFFS_TRACE_ERROR,
				"yaffs tragedy:attempt to put data chunk into a non-file"
				);
			BUG();
		}

		yaffs_chunk_del(dev, nand_chunk, 1, __LINE__);  /* 删除chunk */
		return YAFFS_OK;
	}

	/* 添加或查找第0级tnode */
	tn = yaffs_add_find_tnode_0(dev,
				    &in->variant.file_variant,
				    inode_chunk, NULL);
	if (!tn)
		return YAFFS_FAIL;

	if (!nand_chunk)
		/* 虚拟插入，现在退出 */
		return YAFFS_OK;

	/* 获取现有chunk */
	existing_cunk = yaffs_get_group_base(dev, tn, inode_chunk);

	if (in_scan != 0) {
		/* 如果我们在扫描，那么我们需要测试重复项
		 * 注意：这不需要高效，因为它应该只在写入期间断电时发生，
		 * 那时只有一个chunk会受到影响。
		 *
		 * YAFFS2的修正：这可能经常发生，我们需要考虑效率！TODO
		 * 更新：对于后向扫描，我们不需要重新读取标签，所以这很便宜。
		 */

		if (existing_cunk > 0) {
			/* 注意：现在如果chunk组大小 > 1，现有chunk将不是真正的
			 * chunk_id，因此我们必须执行FindChunkInFile来获取
			 * 真正的chunk id。
			 *
			 * 我们现在有一个重复项，需要决定使用哪一个：
			 *
			 * 后向扫描YAFFS2：我们使用旧的，丢弃新的。
			 * YAFFS1：获取两组标签并比较序列号。
			 */

			if (in_scan > 0) {
				/* 只对前向扫描执行此操作 */
				yaffs_rd_chunk_tags_nand(dev,
							 nand_chunk,
							 NULL, &new_tags);

				/* 执行正确的查找 */
				existing_cunk =
				    yaffs_find_chunk_in_file(in, inode_chunk,
							     &existing_tags);
			}

			if (existing_cunk <= 0) {
				/* 糟糕 - 这是怎么发生的？ */

				yaffs_trace(YAFFS_TRACE_ERROR,
					"yaffs tragedy: existing chunk < 0 in scan"
					);

			}

			/* 注意：删除标志应该为false，否则
			 * 在扫描期间不会加载chunks
			 */

			if (in_scan > 0) {
				new_serial = new_tags.serial_number;
				existing_serial = existing_tags.serial_number;
			}

			if ((in_scan > 0) &&
			    (existing_cunk <= 0 ||
			     ((existing_serial + 1) & 3) == new_serial)) {
				/* 前向扫描。
				 * 使用新的
				 * 删除旧的并继续更新tnode
				 */
				yaffs_chunk_del(dev, existing_cunk, 1,
						__LINE__);
			} else {
				/* 后向扫描或我们想使用现有的
				 * 删除新的并提前返回，这样tnode不会改变
				 */
				yaffs_chunk_del(dev, nand_chunk, 1, __LINE__);
				return YAFFS_OK;
			}
		}

	}

	if (existing_cunk == 0)
		in->n_data_chunks++;  /* 增加数据chunk计数 */

	/* 将chunk加载到tnode中 */
	yaffs_load_tnode_0(dev, tn, inode_chunk, nand_chunk);

	return YAFFS_OK;
}

/* 软删除chunk */
static void yaffs_soft_del_chunk(struct yaffs_dev *dev, int chunk)
{
	struct yaffs_block_info *the_block;
	unsigned block_no;

	yaffs_trace(YAFFS_TRACE_DELETION, "soft delete chunk %d", chunk);

	/* 计算块号 */
	block_no = chunk / dev->param.chunks_per_block;
	the_block = yaffs_get_block_info(dev, block_no);
	if (the_block) {
		the_block->soft_del_pages++;  /* 增加软删除页数 */
		dev->n_free_chunks++;         /* 增加空闲chunk数 */
		yaffs2_update_oldest_dirty_seq(dev, block_no, the_block);  /* 更新最旧脏序列 */
	}
}

/* SoftDeleteWorker向后扫描tnode树并软删除文件中的所有chunks。
 * 软删除所做的只是增加块的软删除计数并将chunk从tnode中拉出。
 * 因此，本质上这与DeleteWorker相同，除了chunks被软删除。
 */

static int yaffs_soft_del_worker(struct yaffs_obj *in, struct yaffs_tnode *tn,
				 u32 level, int chunk_offset)
{
	int i;
	int the_chunk;
	int all_done = 1;  /* 是否全部完成 */
	struct yaffs_dev *dev = in->my_dev;

	if (!tn)
		return 1;

	if (level > 0) {
		/* 处理内部节点 */
		for (i = YAFFS_NTNODES_INTERNAL - 1;
			all_done && i >= 0;
			i--) {
			if (tn->internal[i]) {
				/* 递归处理子节点 */
				all_done =
				    yaffs_soft_del_worker(in,
					tn->internal[i],
					level - 1,
					(chunk_offset <<
					YAFFS_TNODES_INTERNAL_BITS)
					+ i);
				if (all_done) {
					/* 如果子树全部处理完，释放子节点 */
					yaffs_free_tnode(dev,
						tn->internal[i]);
					tn->internal[i] = NULL;
				} else {
					/* 这会发生吗？ */
				}
			}
		}
		return (all_done) ? 1 : 0;
	}

	/* 第0级 */
	 for (i = YAFFS_NTNODES_LEVEL0 - 1; i >= 0; i--) {
		the_chunk = yaffs_get_group_base(dev, tn, i);
		if (the_chunk) {
			yaffs_soft_del_chunk(dev, the_chunk);  /* 软删除chunk */
			yaffs_load_tnode_0(dev, tn, i, 0);     /* 清零tnode条目 */
		}
	}
	return 1;
}

/* 从目录中移除对象 */
static void yaffs_remove_obj_from_dir(struct yaffs_obj *obj)
{
	struct yaffs_dev *dev = obj->my_dev;
	struct yaffs_obj *parent;

	yaffs_verify_obj_in_dir(obj);  /* 验证对象在目录中 */
	parent = obj->parent;

	yaffs_verify_dir(parent);  /* 验证父目录 */

	/* 如果有移除对象回调函数，调用它 */
	if (dev && dev->param.remove_obj_fn)
		dev->param.remove_obj_fn(obj);

	list_del_init(&obj->siblings);  /* 从兄弟列表中删除 */
	obj->parent = NULL;             /* 清除父指针 */

	yaffs_verify_dir(parent);  /* 再次验证父目录 */
}

/* 将对象添加到目录 */
void yaffs_add_obj_to_dir(struct yaffs_obj *directory, struct yaffs_obj *obj)
{
	if (!directory) {
		yaffs_trace(YAFFS_TRACE_ALWAYS,
			"tragedy: Trying to add an object to a null pointer directory"
			);
		BUG();
		return;
	}
	if (directory->variant_type != YAFFS_OBJECT_TYPE_DIRECTORY) {
		yaffs_trace(YAFFS_TRACE_ALWAYS,
			"tragedy: Trying to add an object to a non-directory"
			);
		BUG();
	}

	if (obj->siblings.prev == NULL) {
		/* 未初始化 */
		BUG();
	}

	yaffs_verify_dir(directory);  /* 验证目录 */

	yaffs_remove_obj_from_dir(obj);  /* 先从当前目录移除 */

	/* 现在添加它 */
	list_add(&obj->siblings, &directory->variant.dir_variant.children);
	obj->parent = directory;  /* 设置新的父目录 */

	/* 如果是unlinked或del目录，设置相应标志 */
	if (directory == obj->my_dev->unlinked_dir
	    || directory == obj->my_dev->del_dir) {
		obj->unlinked = 1;                    /* 标记为未链接 */
		obj->my_dev->n_unlinked_files++;      /* 增加未链接文件计数 */
		obj->rename_allowed = 0;              /* 不允许重命名 */
	}

	yaffs_verify_dir(directory);        /* 验证目录 */
	yaffs_verify_obj_in_dir(obj);       /* 验证对象在目录中 */
}

/* 更改对象名称 */
static int yaffs_change_obj_name(struct yaffs_obj *obj,
				 struct yaffs_obj *new_dir,
				 const YCHAR *new_name, int force, int shadows)
{
	int unlink_op;  /* 是否为unlink操作 */
	int del_op;     /* 是否为删除操作 */
	struct yaffs_obj *existing_target;

	if (new_dir == NULL)
		new_dir = obj->parent;	/* 使用旧目录 */

	if (new_dir->variant_type != YAFFS_OBJECT_TYPE_DIRECTORY) {
		yaffs_trace(YAFFS_TRACE_ALWAYS,
			"tragedy: yaffs_change_obj_name: new_dir is not a directory"
			);
		BUG();
	}

	/* 检查操作类型 */
	unlink_op = (new_dir == obj->my_dev->unlinked_dir);
	del_op = (new_dir == obj->my_dev->del_dir);

	/* 查找是否已存在同名对象 */
	existing_target = yaffs_find_by_name(new_dir, new_name);

	/* 如果对象是进入unlinked目录的文件，
	 * 那么可以直接放入，因为重复名称是可以的。
	 * 否则只有在新名称不存在且我们将其放入目录时才继续。
	 */
	if (!(unlink_op || del_op || force ||
	      shadows > 0 || !existing_target) ||
	      new_dir->variant_type != YAFFS_OBJECT_TYPE_DIRECTORY)
		return YAFFS_FAIL;

	yaffs_set_obj_name(obj, new_name);  /* 设置新名称 */
	obj->dirty = 1;                     /* 标记为脏 */
	yaffs_add_obj_to_dir(new_dir, obj); /* 添加到新目录 */

	if (unlink_op)
		obj->unlinked = 1;  /* 标记为未链接 */

	/* 如果是删除操作，则将其标记为gc的收缩 */
	if (yaffs_update_oh(obj, new_name, 0, del_op, shadows, NULL) >= 0)
		return YAFFS_OK;

	return YAFFS_FAIL;
}


/* 从哈希表中移除对象 */
static void yaffs_unhash_obj(struct yaffs_obj *obj)
{
	int bucket;
	struct yaffs_dev *dev = obj->my_dev;

	/* 如果仍然链接在桶列表中，从列表中释放 */
	if (!list_empty(&obj->hash_link)) {
		list_del_init(&obj->hash_link);  /* 从哈希链表中删除 */
		bucket = yaffs_hash_fn(obj->obj_id);  /* 计算桶号 */
		dev->obj_bucket[bucket].count--;     /* 减少桶计数 */
	}
}

/* FreeObject释放一个对象并将其放回空闲列表 */
static void yaffs_free_obj(struct yaffs_obj *obj)
{
	struct yaffs_dev *dev;

	if (!obj) {
		BUG();
		return;
	}
	dev = obj->my_dev;
	yaffs_trace(YAFFS_TRACE_OS, "FreeObject %p inode %p",
		obj, obj->my_inode);
	if (obj->parent)
		BUG();  /* 对象不应该还有父对象 */
	if (!list_empty(&obj->siblings))
		BUG();  /* 对象不应该还在兄弟列表中 */

	if (obj->my_inode) {
		/* 我们仍然连接到缓存的inode。
		 * 现在不删除，但标记为稍后删除
		 */
		obj->defered_free = 1;
		return;
	}

	yaffs_unhash_obj(obj);  /* 从哈希表中移除 */

	yaffs_free_raw_obj(dev, obj);  /* 释放原始对象 */
	dev->n_obj--;                  /* 减少对象计数 */
	dev->checkpoint_blocks_required = 0;	/* 强制重新计算 */
}

/* 处理延迟释放 */
void yaffs_handle_defered_free(struct yaffs_obj *obj)
{
	if (obj->defered_free)
		yaffs_free_obj(obj);  /* 如果标记为延迟释放，现在释放 */
}

/* 通用对象删除 */
static int yaffs_generic_obj_del(struct yaffs_obj *in)
{
	/* 使缓存中的文件数据无效，不刷新。 */
	yaffs_invalidate_file_cache(in);

	if (in->my_dev->param.is_yaffs2 && in->parent != in->my_dev->del_dir) {
		/* 移动到unlinked目录，这样我们就有了删除记录 */
		yaffs_change_obj_name(in, in->my_dev->del_dir, _Y("deleted"), 0,
				      0);
	}

	yaffs_remove_obj_from_dir(in);  /* 从目录中移除对象 */
	yaffs_chunk_del(in->my_dev, in->hdr_chunk, 1, __LINE__);  /* 删除头chunk */
	in->hdr_chunk = 0;  /* 清零头chunk */

	yaffs_free_obj(in);  /* 释放对象 */
	return YAFFS_OK;

}

/* 软删除文件 */
static void yaffs_soft_del_file(struct yaffs_obj *obj)
{
	if (!obj->deleted ||
	    obj->variant_type != YAFFS_OBJECT_TYPE_FILE ||
	    obj->soft_del)
		return;  /* 如果不满足条件，直接返回 */

	if (obj->n_data_chunks <= 0) {
		/* 没有重复对象头的空文件，
		 * 立即删除它 */
		yaffs_free_tnode(obj->my_dev, obj->variant.file_variant.top);
		obj->variant.file_variant.top = NULL;
		yaffs_trace(YAFFS_TRACE_TRACING,
			"yaffs: Deleting empty file %d",
			obj->obj_id);
		yaffs_generic_obj_del(obj);  /* 通用删除 */
	} else {
		/* 对文件进行软删除处理 */
		yaffs_soft_del_worker(obj,
				      obj->variant.file_variant.top,
				      obj->variant.
				      file_variant.top_level, 0);
		obj->soft_del = 1;  /* 标记为软删除 */
	}
}

/* 修剪移除文件结构树中超出文件边界的任何部分
 * （即不指向chunks的部分）。
 *
 * 文件只有在大小减少时才应该被修剪。
 *
 * 在修剪之前，必须从树中拉出chunks，并且
 * 第0级tnode条目必须清零。
 * 也可以用于文件删除，但这可能最好由特殊情况处理。
 *
 * 这个函数是递归的。对于级别 > 0，函数在任何子树上再次调用。
 * 对于级别 == 0，我们只检查子树是否有数据。
 * 如果子树中没有数据，则将其修剪。
 */

static struct yaffs_tnode *yaffs_prune_worker(struct yaffs_dev *dev,
					      struct yaffs_tnode *tn, u32 level,
					      int del0)
{
	int i;
	int has_data;  /* 是否有数据 */

	if (!tn)
		return tn;

	has_data = 0;

	if (level > 0) {
		/* 处理内部节点 */
		for (i = 0; i < YAFFS_NTNODES_INTERNAL; i++) {
			if (tn->internal[i]) {
				/* 递归修剪子树 */
				tn->internal[i] =
				    yaffs_prune_worker(dev,
						tn->internal[i],
						level - 1,
						(i == 0) ? del0 : 1);
			}

			if (tn->internal[i])
				has_data++;  /* 如果子节点存在，标记有数据 */
		}
	} else {
		/* 第0级，检查是否有数据 */
		int tnode_size_u32 = dev->tnode_size / sizeof(u32);
		u32 *map = (u32 *) tn;

		for (i = 0; !has_data && i < tnode_size_u32; i++) {
			if (map[i])
				has_data++;  /* 发现非零数据 */
		}
	}

	if (has_data == 0 && del0) {
		/* 释放并返回NULL */
		yaffs_free_tnode(dev, tn);
		tn = NULL;
	}
	return tn;
}

/* 修剪树 */
static int yaffs_prune_tree(struct yaffs_dev *dev,
			    struct yaffs_file_var *file_struct)
{
	int i;
	int has_data;  /* 是否有数据 */
	int done = 0;  /* 是否完成 */
	struct yaffs_tnode *tn;

	if (file_struct->top_level < 1)
		return YAFFS_OK;

	/* 修剪工作树 */
	file_struct->top =
	   yaffs_prune_worker(dev, file_struct->top, file_struct->top_level, 0);

	/* 现在我们有一个树，所有非零分支都为NULL，但
	 * 高度与之前相同。
	 * 让我们看看是否可以修剪内部tnodes来缩短树。
	 * 如果tnode中只有第0个元素在使用，我们可以这样做
	 * （即所有非零都为NULL）
	 */

	while (file_struct->top_level && !done) {
		tn = file_struct->top;

		has_data = 0;
		/* 检查除第0个元素外是否有数据 */
		for (i = 1; i < YAFFS_NTNODES_INTERNAL; i++) {
			if (tn->internal[i])
				has_data++;
		}

		if (!has_data) {
			/* 只有第0个元素有数据，可以降低树的高度 */
			file_struct->top = tn->internal[0];
			file_struct->top_level--;
			yaffs_free_tnode(dev, tn);  /* 释放当前顶层节点 */
		} else {
			done = 1;  /* 完成修剪 */
		}
	}

	return YAFFS_OK;
}

/*-------------------- 文件结构函数结束 -------------------*/

/* alloc_empty_obj为我们获取一个干净的对象。*/
static struct yaffs_obj *yaffs_alloc_empty_obj(struct yaffs_dev *dev)
{
	struct yaffs_obj *obj = yaffs_alloc_raw_obj(dev);  /* 分配原始对象 */

	if (!obj)
		return obj;

	dev->n_obj++;  /* 增加对象计数 */

	/* 现在美化它... */

	memset(obj, 0, sizeof(struct yaffs_obj));  /* 清零对象 */
	obj->being_created = 1;  /* 标记为正在创建 */

	obj->my_dev = dev;
	obj->hdr_chunk = 0;
	obj->variant_type = YAFFS_OBJECT_TYPE_UNKNOWN;  /* 未知类型 */
	INIT_LIST_HEAD(&(obj->hard_links));  /* 初始化硬链接列表 */
	INIT_LIST_HEAD(&(obj->hash_link));   /* 初始化哈希链接 */
	INIT_LIST_HEAD(&obj->siblings);      /* 初始化兄弟列表 */

	/* 现在使目录合理 */
	if (dev->root_dir) {
		obj->parent = dev->root_dir;  /* 设置父目录为根目录 */
		list_add(&(obj->siblings),
			 &dev->root_dir->variant.dir_variant.children);
	}

	/* 将其添加到lost and found目录。
	 * 注意：不能将root或lost-n-found放入lost-n-found中，所以
	 * 首先检查lost-n-found是否存在
	 */
	if (dev->lost_n_found)
		yaffs_add_obj_to_dir(dev->lost_n_found, obj);

	obj->being_created = 0;  /* 创建完成 */

	dev->checkpoint_blocks_required = 0;	/* 强制重新计算 */

	return obj;
}

/* 查找合适的桶 */
static int yaffs_find_nice_bucket(struct yaffs_dev *dev)
{
	int i;
	int l = 999;        /* 最佳桶索引 */
	int lowest = 999999; /* 最低计数 */

	/* 搜索最短的列表或不太长的列表。
	 */

	for (i = 0; i < 10 && lowest > 4; i++) {
		dev->bucket_finder++;  /* 递增桶查找器 */
		dev->bucket_finder %= YAFFS_NOBJECT_BUCKETS;  /* 循环回绕 */
		if (dev->obj_bucket[dev->bucket_finder].count < lowest) {
			lowest = dev->obj_bucket[dev->bucket_finder].count;
			l = dev->bucket_finder;  /* 记录最佳桶 */
		}
	}

	return l;
}

/* 生成新的对象ID */
static int yaffs_new_obj_id(struct yaffs_dev *dev)
{
	int bucket = yaffs_find_nice_bucket(dev);  /* 找到合适的桶 */
	int found = 0;  /* 是否找到可用ID */
	struct list_head *i;
	u32 n = (u32) bucket;

	/*
	 * 现在通过扫描列表找到一个尚未被占用的对象值，
	 * 每次按桶数递增。
	 */
	while (!found) {
		found = 1;
		n += YAFFS_NOBJECT_BUCKETS;  /* 按桶数递增 */
		list_for_each(i, &dev->obj_bucket[bucket].list) {
			/* 检查这个值是否已被占用 */
			if (i && list_entry(i, struct yaffs_obj,
					    hash_link)->obj_id == n)
				found = 0;  /* 已被占用，继续查找 */
		}
	}
	return n;
}

/* 将对象添加到哈希表 */
static void yaffs_hash_obj(struct yaffs_obj *in)
{
	int bucket = yaffs_hash_fn(in->obj_id);  /* 计算哈希桶 */
	struct yaffs_dev *dev = in->my_dev;

	list_add(&in->hash_link, &dev->obj_bucket[bucket].list);  /* 添加到哈希桶列表 */
	dev->obj_bucket[bucket].count++;  /* 增加桶计数 */
}

/* 根据编号查找对象 */
struct yaffs_obj *yaffs_find_by_number(struct yaffs_dev *dev, u32 number)
{
	int bucket = yaffs_hash_fn(number);  /* 计算哈希桶 */
	struct list_head *i;
	struct yaffs_obj *in;

	/* 遍历哈希桶中的对象 */
	list_for_each(i, &dev->obj_bucket[bucket].list) {
		/* 查看它是否在列表中 */
		in = list_entry(i, struct yaffs_obj, hash_link);
		if (in->obj_id == number) {
			/* 如果是延迟释放的，不显示 */
			if (in->defered_free)
				return NULL;
			return in;
		}
	}

	return NULL;  /* 未找到 */
}

/* 创建新对象 */
static struct yaffs_obj *yaffs_new_obj(struct yaffs_dev *dev, int number,
				enum yaffs_obj_type type)
{
	struct yaffs_obj *the_obj = NULL;
	struct yaffs_tnode *tn = NULL;

	if (number < 0)
		number = yaffs_new_obj_id(dev);  /* 生成新的对象ID */

	if (type == YAFFS_OBJECT_TYPE_FILE) {
		tn = yaffs_get_tnode(dev);  /* 为文件获取tnode */
		if (!tn)
			return NULL;
	}

	the_obj = yaffs_alloc_empty_obj(dev);  /* 分配空对象 */
	if (!the_obj) {
		if (tn)
			yaffs_free_tnode(dev, tn);  /* 释放tnode */
		return NULL;
	}

	the_obj->fake = 0;              /* 不是虚假对象 */
	the_obj->rename_allowed = 1;    /* 允许重命名 */
	the_obj->unlink_allowed = 1;    /* 允许取消链接 */
	the_obj->obj_id = number;       /* 设置对象ID */
	yaffs_hash_obj(the_obj);        /* 添加到哈希表 */
	the_obj->variant_type = type;   /* 设置对象类型 */
	yaffs_load_current_time(the_obj, 1, 1);  /* 加载当前时间 */

	switch (type) {
	case YAFFS_OBJECT_TYPE_FILE:
		the_obj->variant.file_variant.file_size = 0;      /* 文件大小为0 */
		the_obj->variant.file_variant.stored_size = 0;    /* 存储大小为0 */
		the_obj->variant.file_variant.shrink_size =
						yaffs_max_file_size(dev);  /* 设置收缩大小 */
		the_obj->variant.file_variant.top_level = 0;      /* 顶层级别为0 */
		the_obj->variant.file_variant.top = tn;           /* 设置顶层tnode */
		break;
	case YAFFS_OBJECT_TYPE_DIRECTORY:
		INIT_LIST_HEAD(&the_obj->variant.dir_variant.children);  /* 初始化子对象列表 */
		INIT_LIST_HEAD(&the_obj->variant.dir_variant.dirty);     /* 初始化脏列表 */
		break;
	case YAFFS_OBJECT_TYPE_SYMLINK:
	case YAFFS_OBJECT_TYPE_HARDLINK:
	case YAFFS_OBJECT_TYPE_SPECIAL:
		/* 无需操作 */
		break;
	case YAFFS_OBJECT_TYPE_UNKNOWN:
		/* todo 这不应该发生 */
		break;
	}
	return the_obj;
}

/* 创建虚假目录 */
static struct yaffs_obj *yaffs_create_fake_dir(struct yaffs_dev *dev,
					       int number, u32 mode)
{

	struct yaffs_obj *obj =
	    yaffs_new_obj(dev, number, YAFFS_OBJECT_TYPE_DIRECTORY);

	if (!obj)
		return NULL;

	obj->fake = 1;	/* 它是虚假的，所以可能不使用NAND */
	obj->rename_allowed = 0;  /* 不允许重命名 */
	obj->unlink_allowed = 0;  /* 不允许取消链接 */
	obj->deleted = 0;         /* 未删除 */
	obj->unlinked = 0;        /* 未取消链接 */
	obj->yst_mode = mode;     /* 设置模式 */
	obj->my_dev = dev;        /* 设置设备 */
	obj->hdr_chunk = 0;	/* 不是有效的chunk */
	return obj;

}


/* 初始化tnodes和对象 */
static void yaffs_init_tnodes_and_objs(struct yaffs_dev *dev)
{
	int i;

	dev->n_obj = 0;      /* 对象数为0 */
	dev->n_tnodes = 0;   /* tnode数为0 */
	yaffs_init_raw_tnodes_and_objs(dev);  /* 初始化原始tnodes和对象 */

	/* 初始化所有对象桶 */
	for (i = 0; i < YAFFS_NOBJECT_BUCKETS; i++) {
		INIT_LIST_HEAD(&dev->obj_bucket[i].list);  /* 初始化桶列表 */
		dev->obj_bucket[i].count = 0;              /* 桶计数为0 */
	}
}

/* 根据编号查找或创建对象 */
struct yaffs_obj *yaffs_find_or_create_by_number(struct yaffs_dev *dev,
						 int number,
						 enum yaffs_obj_type type)
{
	struct yaffs_obj *the_obj = NULL;

	if (number > 0)
		the_obj = yaffs_find_by_number(dev, number);  /* 先尝试查找 */

	if (!the_obj)
		the_obj = yaffs_new_obj(dev, number, type);   /* 如果未找到，创建新对象 */

	return the_obj;

}

/* 克隆字符串 */
YCHAR *yaffs_clone_str(const YCHAR *str)
{
	YCHAR *new_str = NULL;
	int len;

	if (!str)
		str = _Y("");  /* 如果为NULL，使用空字符串 */

	len = strnlen(str, YAFFS_MAX_ALIAS_LENGTH);  /* 获取字符串长度 */
	new_str = kmalloc((len + 1) * sizeof(YCHAR), GFP_NOFS);  /* 分配内存 */
	if (new_str) {
		strncpy(new_str, str, len);  /* 复制字符串 */
		new_str[len] = 0;            /* 添加结束符 */
	}
	return new_str;

}
/*
 * yaffs_update_parent() 处理当在目录中创建或删除新链接（即名称）时
 * 修复目录的mtime和ctime。
 *
 * 即：
 *   create dir/a : 更新dir的mtime/ctime
 *   rm dir/a:   更新dir的mtime/ctime
 *   modify dir/a: 不更新dir的mtime/ctime
 *
 * 这可以立即处理或延迟处理。延迟有助于减少在短时间内
 * 目录中许多文件发生更改时的更新次数。
 *
 * 如果目录更新被延迟，则必须定期调用yaffs_update_dirty_dirs。
 */

static void yaffs_update_parent(struct yaffs_obj *obj)
{
	struct yaffs_dev *dev;

	if (!obj)
		return;
	dev = obj->my_dev;
	obj->dirty = 1;  /* 标记为脏 */
	yaffs_load_current_time(obj, 0, 1);  /* 加载当前时间 */
	if (dev->param.defered_dir_update) {
		struct list_head *link = &obj->variant.dir_variant.dirty;

		if (list_empty(link)) {
			list_add(link, &dev->dirty_dirs);  /* 添加到脏目录列表 */
			yaffs_trace(YAFFS_TRACE_BACKGROUND,
			  "Added object %d to dirty directories",
			   obj->obj_id);
		}

	} else {
		yaffs_update_oh(obj, NULL, 0, 0, 0, NULL);  /* 立即更新对象头 */
	}
}

/* 更新脏目录 */
void yaffs_update_dirty_dirs(struct yaffs_dev *dev)
{
	struct list_head *link;
	struct yaffs_obj *obj;
	struct yaffs_dir_var *d_s;
	union yaffs_obj_var *o_v;

	yaffs_trace(YAFFS_TRACE_BACKGROUND, "Update dirty directories");

	/* 处理所有脏目录 */
	while (!list_empty(&dev->dirty_dirs)) {
		link = dev->dirty_dirs.next;
		list_del_init(link);  /* 从脏目录列表中删除 */

		/* 通过链表条目获取对象 */
		d_s = list_entry(link, struct yaffs_dir_var, dirty);
		o_v = list_entry(d_s, union yaffs_obj_var, dir_variant);
		obj = list_entry(o_v, struct yaffs_obj, variant);

		yaffs_trace(YAFFS_TRACE_BACKGROUND, "Update directory %d",
			obj->obj_id);

		if (obj->dirty)
			yaffs_update_oh(obj, NULL, 0, 0, 0, NULL);  /* 更新对象头 */
	}
}

/*
 * Mknod（创建）一个新对象。
 * equiv_obj只对硬链接有意义；
 * alias_str只对符号链接有意义。
 * rdev只对设备有意义（特殊对象的子集）
 */

static struct yaffs_obj *yaffs_create_obj(enum yaffs_obj_type type,
					  struct yaffs_obj *parent,
					  const YCHAR *name,
					  u32 mode,
					  u32 uid,
					  u32 gid,
					  struct yaffs_obj *equiv_obj,
					  const YCHAR *alias_str, u32 rdev)
{
	struct yaffs_obj *in;
	YCHAR *str = NULL;
	struct yaffs_dev *dev = parent->my_dev;

	/* 检查条目是否存在。
	 * 如果存在则调用失败，因为我们不想要重复。 */
	if (yaffs_find_by_name(parent, name))
		return NULL;

	if (type == YAFFS_OBJECT_TYPE_SYMLINK) {
		str = yaffs_clone_str(alias_str);  /* 克隆别名字符串 */
		if (!str)
			return NULL;
	}

	in = yaffs_new_obj(dev, -1, type);  /* 创建新对象 */

	if (!in) {
		kfree(str);  /* 释放字符串 */
		return NULL;
	}

	in->hdr_chunk = 0;        /* 头chunk为0 */
	in->valid = 1;            /* 标记为有效 */
	in->variant_type = type;  /* 设置变体类型 */

	in->yst_mode = mode;      /* 设置模式 */

	yaffs_attribs_init(in, gid, uid, rdev);  /* 初始化属性 */

	in->n_data_chunks = 0;    /* 数据chunk数为0 */

	yaffs_set_obj_name(in, name);  /* 设置对象名称 */
	in->dirty = 1;                 /* 标记为脏 */

	yaffs_add_obj_to_dir(parent, in);  /* 添加到父目录 */

	in->my_dev = parent->my_dev;  /* 设置设备 */

	switch (type) {
	case YAFFS_OBJECT_TYPE_SYMLINK:
		in->variant.symlink_variant.alias = str;  /* 设置符号链接别名 */
		break;
	case YAFFS_OBJECT_TYPE_HARDLINK:
		in->variant.hardlink_variant.equiv_obj = equiv_obj;        /* 设置等价对象 */
		in->variant.hardlink_variant.equiv_id = equiv_obj->obj_id; /* 设置等价ID */
		list_add(&in->hard_links, &equiv_obj->hard_links);         /* 添加到硬链接列表 */
		break;
	case YAFFS_OBJECT_TYPE_FILE:
	case YAFFS_OBJECT_TYPE_DIRECTORY:
	case YAFFS_OBJECT_TYPE_SPECIAL:
	case YAFFS_OBJECT_TYPE_UNKNOWN:
		/* 无需操作 */
		break;
	}

	if (yaffs_update_oh(in, name, 0, 0, 0, NULL) < 0) {
		/* 无法创建对象头，失败 */
		yaffs_del_obj(in);
		in = NULL;
	}

	if (in)
		yaffs_update_parent(parent);  /* 更新父目录 */

	return in;
}

/* 创建文件 */
struct yaffs_obj *yaffs_create_file(struct yaffs_obj *parent,
				    const YCHAR *name, u32 mode, u32 uid,
				    u32 gid)
{
	return yaffs_create_obj(YAFFS_OBJECT_TYPE_FILE, parent, name, mode,
				uid, gid, NULL, NULL, 0);
}

/* 创建目录 */
struct yaffs_obj *yaffs_create_dir(struct yaffs_obj *parent, const YCHAR *name,
				   u32 mode, u32 uid, u32 gid)
{
	return yaffs_create_obj(YAFFS_OBJECT_TYPE_DIRECTORY, parent, name,
				mode, uid, gid, NULL, NULL, 0);
}

/* 创建特殊文件 */
struct yaffs_obj *yaffs_create_special(struct yaffs_obj *parent,
				       const YCHAR *name, u32 mode, u32 uid,
				       u32 gid, u32 rdev)
{
	return yaffs_create_obj(YAFFS_OBJECT_TYPE_SPECIAL, parent, name, mode,
				uid, gid, NULL, NULL, rdev);
}

/* 创建符号链接 */
struct yaffs_obj *yaffs_create_symlink(struct yaffs_obj *parent,
				       const YCHAR *name, u32 mode, u32 uid,
				       u32 gid, const YCHAR *alias)
{
	return yaffs_create_obj(YAFFS_OBJECT_TYPE_SYMLINK, parent, name, mode,
				uid, gid, NULL, alias, 0);
}

/* yaffs_link_obj返回等价对象的对象id。*/
struct yaffs_obj *yaffs_link_obj(struct yaffs_obj *parent, const YCHAR * name,
				 struct yaffs_obj *equiv_obj)
{
	/* 获取真实对象，以防我们被传入了硬链接对象 */
	equiv_obj = yaffs_get_equivalent_obj(equiv_obj);

	if (yaffs_create_obj(YAFFS_OBJECT_TYPE_HARDLINK,
			parent, name, 0, 0, 0,
			equiv_obj, NULL, 0))
		return equiv_obj;

	return NULL;

}



/*---------------------- 块管理和页分配 -------------*/

/* 反初始化块 */
static void yaffs_deinit_blocks(struct yaffs_dev *dev)
{
	if (dev->block_info_alt && dev->block_info)
		vfree(dev->block_info);  /* 使用vmalloc分配的，用vfree释放 */
	else
		kfree(dev->block_info);  /* 使用kmalloc分配的，用kfree释放 */

	dev->block_info_alt = 0;  /* 重置替代标志 */

	dev->block_info = NULL;   /* 清空指针 */

	if (dev->chunk_bits_alt && dev->chunk_bits)
		vfree(dev->chunk_bits);  /* 使用vmalloc分配的，用vfree释放 */
	else
		kfree(dev->chunk_bits);  /* 使用kmalloc分配的，用kfree释放 */
	dev->chunk_bits_alt = 0;     /* 重置替代标志 */
	dev->chunk_bits = NULL;      /* 清空指针 */
}

/* 初始化块 */
static int yaffs_init_blocks(struct yaffs_dev *dev)
{
	int n_blocks = dev->internal_end_block - dev->internal_start_block + 1;  /* 计算块数 */

	dev->block_info = NULL;
	dev->chunk_bits = NULL;
	dev->alloc_block = -1;	/* 强制获取新的块 */

	/* 如果第一种分配策略失败，尝试替代方案 */
	dev->block_info =
		kmalloc(n_blocks * sizeof(struct yaffs_block_info), GFP_NOFS);
	if (!dev->block_info) {
		/* kmalloc失败，尝试vmalloc */
		dev->block_info =
		    vmalloc(n_blocks * sizeof(struct yaffs_block_info));
		dev->block_info_alt = 1;  /* 标记使用了替代分配 */
	} else {
		dev->block_info_alt = 0;  /* 使用标准分配 */
	}

	if (!dev->block_info)
		goto alloc_error;  /* 分配失败 */

	/* 设置动态块信息。向上舍入字节数。 */
	dev->chunk_bit_stride = (dev->param.chunks_per_block + 7) / 8;
	dev->chunk_bits =
		kmalloc(dev->chunk_bit_stride * n_blocks, GFP_NOFS);
	if (!dev->chunk_bits) {
		/* kmalloc失败，尝试vmalloc */
		dev->chunk_bits =
		    vmalloc(dev->chunk_bit_stride * n_blocks);
		dev->chunk_bits_alt = 1;  /* 标记使用了替代分配 */
	} else {
		dev->chunk_bits_alt = 0;  /* 使用标准分配 */
	}
	if (!dev->chunk_bits)
		goto alloc_error;  /* 分配失败 */


	/* 清零分配的内存 */
	memset(dev->block_info, 0, n_blocks * sizeof(struct yaffs_block_info));
	memset(dev->chunk_bits, 0, dev->chunk_bit_stride * n_blocks);
	return YAFFS_OK;

alloc_error:
	yaffs_deinit_blocks(dev);  /* 清理已分配的内存 */
	return YAFFS_FAIL;
}


/* 块变脏处理 */
void yaffs_block_became_dirty(struct yaffs_dev *dev, int block_no)
{
	struct yaffs_block_info *bi = yaffs_get_block_info(dev, block_no);
	int erased_ok = 0;  /* 擦除是否成功 */
	u32 i;

	/* 如果块仍然健康，擦除它并标记为干净。
	 * 如果块有数据故障，则退役它。
	 */

	yaffs_trace(YAFFS_TRACE_GC | YAFFS_TRACE_ERASE,
		"yaffs_block_became_dirty block %d state %d %s",
		block_no, bi->block_state,
		(bi->needs_retiring) ? "needs retiring" : "");

	yaffs2_clear_oldest_dirty_seq(dev, bi);  /* 清除最旧的脏序列 */

	bi->block_state = YAFFS_BLOCK_STATE_DIRTY;  /* 设置块状态为脏 */

	/* 如果这是正在进行垃圾回收的块，则停止垃圾回收 */
	if (block_no == (int)dev->gc_block)
		dev->gc_block = 0;

	/* 如果这个块当前是垃圾回收的最佳候选者，
	 * 则取消候选资格 */
	if (block_no == (int)dev->gc_dirtiest) {
		dev->gc_dirtiest = 0;      /* 清除最脏块 */
		dev->gc_pages_in_use = 0;  /* 清除使用中的页数 */
	}

	if (!bi->needs_retiring) {
		yaffs2_checkpt_invalidate(dev);  /* 使检查点无效 */
		erased_ok = yaffs_erase_block(dev, block_no);  /* 擦除块 */
		if (!erased_ok) {
			dev->n_erase_failures++;  /* 增加擦除失败计数 */
			yaffs_trace(YAFFS_TRACE_ERROR | YAFFS_TRACE_BAD_BLOCKS,
			  "**>> Erasure failed %d", block_no);
		}
	}

	/* 如果需要，验证擦除 */
	if (erased_ok &&
	    ((yaffs_trace_mask & YAFFS_TRACE_ERASE) ||
	     !yaffs_skip_verification(dev))) {
		for (i = 0; i < dev->param.chunks_per_block; i++) {
			if (!yaffs_check_chunk_erased(dev,
				block_no * dev->param.chunks_per_block + i)) {
				yaffs_trace(YAFFS_TRACE_ERROR,
					">>Block %d erasure supposedly OK, but chunk %d not erased",
					block_no, i);
			}
		}
	}

	if (!erased_ok) {
		/* 我们失去了一个空闲空间块 */
		dev->n_free_chunks -= dev->param.chunks_per_block;
		yaffs_retire_block(dev, block_no);  /* 退役块 */
		yaffs_trace(YAFFS_TRACE_ERROR | YAFFS_TRACE_BAD_BLOCKS,
			"**>> Block %d retired", block_no);
		return;
	}

	/* 清理它... */
	bi->block_state = YAFFS_BLOCK_STATE_EMPTY;  /* 设置为空状态 */
	bi->seq_number = 0;                         /* 序列号清零 */
	dev->n_erased_blocks++;                     /* 增加已擦除块计数 */
	bi->pages_in_use = 0;                       /* 使用中的页数清零 */
	bi->soft_del_pages = 0;                     /* 软删除页数清零 */
	bi->has_shrink_hdr = 0;                     /* 清除收缩头标志 */
	bi->skip_erased_check = 1;	/* 干净，所以无需检查 */
	bi->gc_prioritise = 0;                      /* 清除垃圾回收优先级 */
	bi->has_summary = 0;                        /* 清除摘要标志 */

	yaffs_clear_chunk_bits(dev, block_no);      /* 清除chunk位 */

	yaffs_trace(YAFFS_TRACE_ERASE, "Erased block %d", block_no);
}

/* 垃圾回收处理chunk */
static inline int yaffs_gc_process_chunk(struct yaffs_dev *dev,
					struct yaffs_block_info *bi,
					int old_chunk, u8 *buffer)
{
	int new_chunk;
	int mark_flash = 1;      /* 是否标记flash */
	struct yaffs_ext_tags tags;
	struct yaffs_obj *object;
	int matching_chunk;      /* 匹配的chunk */
	int ret_val = YAFFS_OK;

	memset(&tags, 0, sizeof(tags));
	yaffs_rd_chunk_tags_nand(dev, old_chunk,
				 buffer, &tags);  /* 读取chunk标签 */
	object = yaffs_find_by_number(dev, tags.obj_id);  /* 根据对象ID查找对象 */

	yaffs_trace(YAFFS_TRACE_GC_DETAIL,
		"Collecting chunk in block %d, %d %d %d ",
		dev->gc_chunk, tags.obj_id,
		tags.chunk_id, tags.n_bytes);

	if (object && !yaffs_skip_verification(dev)) {
		if (tags.chunk_id == 0)
			matching_chunk =
			    object->hdr_chunk;  /* 对象头chunk */
		else if (object->soft_del)
			/* 绕过测试 */
			matching_chunk = old_chunk;
		else
			matching_chunk =
			    yaffs_find_chunk_in_file
			    (object, tags.chunk_id,
			     NULL);  /* 在文件中查找chunk */

		if (old_chunk != matching_chunk)
			yaffs_trace(YAFFS_TRACE_ERROR,
				"gc: page in gc mismatch: %d %d %d %d",
				old_chunk,
				matching_chunk,
				tags.obj_id,
				tags.chunk_id);
	}

	if (!object) {
		yaffs_trace(YAFFS_TRACE_ERROR,
			"page %d in gc has no object: %d %d %d ",
			old_chunk,
			tags.obj_id, tags.chunk_id,
			tags.n_bytes);
	}

	if (object &&
	    object->deleted &&
	    object->soft_del && tags.chunk_id != 0) {
		/* 软删除文件中的数据chunk，
		 * 丢弃它。
		 * 这是一个软删除的数据chunk，
		 * 无需复制，只需忘记它并修复对象。
		 */

		/* 空闲chunks已经包括了
		 * 软删除的chunks，但是这个
		 * chunk很快就会被真正删除，
		 * 这将增加空闲chunks。我们必须
		 * 减少空闲chunks，这样才能正常工作。
		 */
		dev->n_free_chunks--;   /* 减少空闲chunk数 */
		bi->soft_del_pages--;   /* 减少软删除页数 */

		object->n_data_chunks--;  /* 减少对象数据chunk数 */
		if (object->n_data_chunks <= 0) {
			/* 记住要清理对象 */
			dev->gc_cleanup_list[dev->n_clean_ups] = tags.obj_id;
			dev->n_clean_ups++;
		}
		mark_flash = 0;  /* 不标记flash */
	} else if (object) {
		/* 这要么是活动文件中的数据chunk，
		 * 要么是对象头，所以我们对它感兴趣。
		 * 注意：需要保留已删除文件的对象头，
		 * 直到整个文件被删除
		 */
		tags.serial_number++;  /* 增加序列号 */
		dev->n_gc_copies++;    /* 增加垃圾回收复制计数 */

		if (tags.chunk_id == 0) {
			/* 这是一个对象ID，
			 * 我们需要清除收缩头标志，因为它的工作已经完成。
			 * 还需要清理阴影。
			 * 注意：我们不想做所有的对象头字节序转换工作，
			 * 所以我们保持oh的存储字节序。
			 */

			struct yaffs_obj_hdr *oh;
			oh = (struct yaffs_obj_hdr *) buffer;

			oh->is_shrink = 0;                /* 清除收缩标志 */
			tags.extra_is_shrink = 0;
			oh->shadows_obj = 0;              /* 清除阴影对象 */
			oh->inband_shadowed_obj_id = 0;
			tags.extra_shadows = 0;

			/* 更新文件大小 */
			if (object->variant_type == YAFFS_OBJECT_TYPE_FILE) {
				yaffs_oh_size_load(dev, oh,
				    object->variant.file_variant.stored_size, 1);
				tags.extra_file_size =
				    object->variant.file_variant.stored_size;
			}

			yaffs_verify_oh(object, oh, &tags, 1);  /* 验证对象头 */
			new_chunk =
			    yaffs_write_new_chunk(dev, (u8 *) oh, &tags, 1);
		} else {
			new_chunk =
			    yaffs_write_new_chunk(dev, buffer, &tags, 1);
		}

		if (new_chunk < 0) {
			ret_val = YAFFS_FAIL;
		} else {

			/* 现在修复Tnodes等 */

			if (tags.chunk_id == 0) {
				/* 这是一个头 */
				object->hdr_chunk = new_chunk;        /* 设置新的头chunk */
				object->serial = tags.serial_number;  /* 设置序列号 */
			} else {
				/* 这是一个数据chunk */
				yaffs_put_chunk_in_file(object, tags.chunk_id,
							new_chunk, 0);
			}
		}
	}
	if (ret_val == YAFFS_OK)
		yaffs_chunk_del(dev, old_chunk, mark_flash, __LINE__);  /* 删除旧chunk */
	return ret_val;
}

/* 垃圾回收块 */
static int yaffs_gc_block(struct yaffs_dev *dev, int block, int whole_block)
{
	int old_chunk;
	int ret_val = YAFFS_OK;
	u32 i;
	int is_checkpt_block;  /* 是否为检查点块 */
	int max_copies;        /* 最大复制数 */
	int chunks_before = yaffs_get_erased_chunks(dev);  /* 垃圾回收前的已擦除chunk数 */
	int chunks_after;      /* 垃圾回收后的已擦除chunk数 */
	struct yaffs_block_info *bi = yaffs_get_block_info(dev, block);

	is_checkpt_block = (bi->block_state == YAFFS_BLOCK_STATE_CHECKPOINT);

	yaffs_trace(YAFFS_TRACE_TRACING,
		"Collecting block %d, in use %d, shrink %d, whole_block %d",
		block, bi->pages_in_use, bi->has_shrink_hdr,
		whole_block);

	/*yaffs_verify_free_chunks(dev); */

	if (bi->block_state == YAFFS_BLOCK_STATE_FULL)
		bi->block_state = YAFFS_BLOCK_STATE_COLLECTING;  /* 设置为回收中状态 */

	bi->has_shrink_hdr = 0;	/* 清除标志，以便块可以擦除 */

	dev->gc_disable = 1;  /* 禁用垃圾回收 */

	yaffs_summary_gc(dev, block);  /* 处理摘要垃圾回收 */

	if (is_checkpt_block || !yaffs_still_some_chunks(dev, block)) {
		yaffs_trace(YAFFS_TRACE_TRACING,
			"Collecting block %d that has no chunks in use",
			block);
		yaffs_block_became_dirty(dev, block);  /* 块变脏 */
	} else {

		u8 *buffer = yaffs_get_temp_buffer(dev);  /* 获取临时缓冲区 */

		yaffs_verify_blk(dev, bi, block);  /* 验证块 */

		max_copies = (whole_block) ? dev->param.chunks_per_block : 5;
		old_chunk = block * dev->param.chunks_per_block + dev->gc_chunk;

		/* 遍历块中的所有chunk */
		for (/* init already done */ ;
		     ret_val == YAFFS_OK &&
		     dev->gc_chunk < dev->param.chunks_per_block &&
		     (bi->block_state == YAFFS_BLOCK_STATE_COLLECTING) &&
		     max_copies > 0;
		     dev->gc_chunk++, old_chunk++) {
			if (yaffs_check_chunk_bit(dev, block, dev->gc_chunk)) {
				/* 页面正在使用中，可能需要复制 */
				max_copies--;
				ret_val = yaffs_gc_process_chunk(dev, bi,
							old_chunk, buffer);
			}
		}
		yaffs_release_temp_buffer(dev, buffer);  /* 释放临时缓冲区 */
	}

	yaffs_verify_collected_blk(dev, bi, block);  /* 验证回收的块 */

	if (bi->block_state == YAFFS_BLOCK_STATE_COLLECTING) {
		/*
		 * 垃圾回收未完成。将块状态设置回FULL，
		 * 因为检查点不会恢复垃圾回收。
		 */
		bi->block_state = YAFFS_BLOCK_STATE_FULL;
	} else {
		/* 垃圾回收完成 */
		/* 执行任何必需的清理 */
		for (i = 0; i < dev->n_clean_ups; i++) {
			/* 是时候删除文件了 */
			struct yaffs_obj *object =
			    yaffs_find_by_number(dev, dev->gc_cleanup_list[i]);
			if (object) {
				yaffs_free_tnode(dev,
					  object->variant.file_variant.top);
				object->variant.file_variant.top = NULL;
				yaffs_trace(YAFFS_TRACE_GC,
					"yaffs: About to finally delete object %d",
					object->obj_id);
				yaffs_generic_obj_del(object);  /* 通用对象删除 */
				object->my_dev->n_deleted_files--;
			}

		}
		chunks_after = yaffs_get_erased_chunks(dev);  /* 获取回收后的已擦除chunk数 */
		if (chunks_before >= chunks_after)
			yaffs_trace(YAFFS_TRACE_GC,
				"gc did not increase free chunks before %d after %d",
				chunks_before, chunks_after);
		dev->gc_block = 0;     /* 重置垃圾回收块 */
		dev->gc_chunk = 0;     /* 重置垃圾回收chunk */
		dev->n_clean_ups = 0;  /* 重置清理计数 */
	}

	dev->gc_disable = 0;  /* 重新启用垃圾回收 */

	return ret_val;
}

/*
 * find_gc_block() 选择最脏的块（或足够接近的）
 * 进行垃圾回收。
 */

static unsigned yaffs_find_gc_block(struct yaffs_dev *dev,
				    int aggressive, int background)
{
	u32 i;
	u32 iterations;        /* 迭代次数 */
	u32 selected = 0;      /* 选中的块 */
	int prioritised = 0;   /* 是否优先 */
	int prioritised_exist = 0;  /* 是否存在优先块 */
	struct yaffs_block_info *bi;
	u32 threshold = dev->param.chunks_per_block;  /* 阈值 */

	(void) prioritised;  /* 避免编译器警告 */

	/* 首先看看我们是否需要获取一个优先块 */
	if (dev->has_pending_prioritised_gc && !aggressive) {
		dev->gc_dirtiest = 0;
		bi = dev->block_info;
		for (i = dev->internal_start_block;
		     i <= dev->internal_end_block && !selected; i++) {

			if (bi->gc_prioritise) {
				prioritised_exist = 1;  /* 存在优先块 */
				if (bi->block_state == YAFFS_BLOCK_STATE_FULL &&
				    yaffs_block_ok_for_gc(dev, bi)) {
					selected = i;      /* 选择这个块 */
					prioritised = 1;   /* 标记为优先 */
				}
			}
			bi++;
		}

		/*
		 * 如果有优先块但没有被选中，那么
		 * 这是因为至少有一个旧的脏块阻塞了工作。
		 * 让我们回收最旧的脏块。
		 */

		if (prioritised_exist &&
		    !selected && dev->oldest_dirty_block > 0)
			selected = dev->oldest_dirty_block;

		if (!prioritised_exist)	/* 没有找到，所以我们可以清除这个标志 */
			dev->has_pending_prioritised_gc = 0;
	}

	/* 如果我们正在进行激进的垃圾回收，那么我们乐意接受不太脏的块，
	 * 并且搜索得更努力。
	 * 否则（悠闲的垃圾回收），我们只有在块只有少数几页在使用时
	 * 才会这样做。
	 */

	if (!selected) {
		u32 pages_used;  /* 使用的页数 */
		int n_blocks =
		    dev->internal_end_block - dev->internal_start_block + 1;  /* 块总数 */
		if (aggressive) {
			threshold = dev->param.chunks_per_block;  /* 激进模式：阈值为每块chunk数 */
			iterations = n_blocks;                    /* 迭代所有块 */
		} else {
			u32 max_threshold;  /* 最大阈值 */

			if (background)
				max_threshold = dev->param.chunks_per_block / 2;  /* 后台模式：一半 */
			else
				max_threshold = dev->param.chunks_per_block / 8;  /* 前台模式：八分之一 */

			if (max_threshold < YAFFS_GC_PASSIVE_THRESHOLD)
				max_threshold = YAFFS_GC_PASSIVE_THRESHOLD;  /* 确保不低于被动阈值 */

			/* 计算阈值 */
			threshold = background ? (dev->gc_not_done + 2) * 2 : 0;
			if (threshold < YAFFS_GC_PASSIVE_THRESHOLD)
				threshold = YAFFS_GC_PASSIVE_THRESHOLD;
			if (threshold > max_threshold)
				threshold = max_threshold;

			iterations = n_blocks / 16 + 1;  /* 迭代次数为块数的1/16 */
			if (iterations > 100)
				iterations = 100;  /* 最多100次迭代 */
		}

		/* 搜索合适的垃圾回收块 */
		for (i = 0;
		     i < iterations &&
		     (dev->gc_dirtiest < 1 ||
		      dev->gc_pages_in_use > YAFFS_GC_GOOD_ENOUGH);
		     i++) {
			dev->gc_block_finder++;  /* 递增块查找器 */
			if (dev->gc_block_finder < dev->internal_start_block ||
			    dev->gc_block_finder > dev->internal_end_block)
				dev->gc_block_finder =
				    dev->internal_start_block;  /* 循环回到起始块 */

			bi = yaffs_get_block_info(dev, dev->gc_block_finder);

			pages_used = bi->pages_in_use - bi->soft_del_pages;  /* 计算实际使用的页数 */

			/* 检查是否为合适的垃圾回收候选块 */
			if (bi->block_state == YAFFS_BLOCK_STATE_FULL &&
			    pages_used < dev->param.chunks_per_block &&
			    (dev->gc_dirtiest < 1 ||
			     pages_used < dev->gc_pages_in_use) &&
			    yaffs_block_ok_for_gc(dev, bi)) {
				dev->gc_dirtiest = dev->gc_block_finder;  /* 记录最脏的块 */
				dev->gc_pages_in_use = pages_used;        /* 记录使用的页数 */
			}
		}

		if (dev->gc_dirtiest > 0 && dev->gc_pages_in_use <= threshold)
			selected = dev->gc_dirtiest;  /* 选择最脏的块 */
	}

	/*
	 * 如果一段时间内没有选择任何块，尝试最旧的脏块，
	 * 因为它阻塞了工作。
	 */

	if (!selected && dev->param.is_yaffs2 &&
	    dev->gc_not_done >= (background ? 10 : 20)) {
		yaffs2_find_oldest_dirty_seq(dev);  /* 查找最旧的脏序列 */
		if (dev->oldest_dirty_block > 0) {
			selected = dev->oldest_dirty_block;  /* 选择最旧的脏块 */
			dev->gc_dirtiest = selected;
			dev->oldest_dirty_gc_count++;        /* 增加最旧脏块垃圾回收计数 */
			bi = yaffs_get_block_info(dev, selected);
			dev->gc_pages_in_use =
			    bi->pages_in_use - bi->soft_del_pages;  /* 计算使用的页数 */
		} else {
			dev->gc_not_done = 0;  /* 重置未完成计数 */
		}
	}

	if (selected) {
		yaffs_trace(YAFFS_TRACE_GC,
			"GC Selected block %d with %d free, prioritised:%d",
			selected,
			dev->param.chunks_per_block - dev->gc_pages_in_use,
			prioritised);

		dev->n_gc_blocks++;  /* 增加垃圾回收块计数 */
		if (background)
			dev->bg_gcs++;   /* 增加后台垃圾回收计数 */

		dev->gc_dirtiest = 0;      /* 重置最脏块 */
		dev->gc_pages_in_use = 0;  /* 重置使用页数 */
		dev->gc_not_done = 0;      /* 重置未完成计数 */
		if (dev->refresh_skip > 0)
			dev->refresh_skip--;   /* 减少刷新跳过计数 */
	} else {
		dev->gc_not_done++;  /* 增加未完成计数 */
		yaffs_trace(YAFFS_TRACE_GC,
			"GC none: finder %d skip %d threshold %d dirtiest %d using %d oldest %d%s",
			dev->gc_block_finder, dev->gc_not_done, threshold,
			dev->gc_dirtiest, dev->gc_pages_in_use,
			dev->oldest_dirty_block, background ? " bg" : "");
	}

	return selected;  /* 返回选中的块 */
}

/* 新的垃圾回收器
 * 如果已擦除块非常少，那么我们进行激进的垃圾回收，
 * 否则我们进行"悠闲的"垃圾回收。
 * 激进的垃圾回收查看更远（整个数组）并接受不太脏的块。
 * 被动的垃圾回收只检查较小的区域，只接受更脏的块。
 *
 * 这个想法是帮助以更分散的方式清理空间。
 * 不知道它是否真的有用。
 */
static int yaffs_check_gc(struct yaffs_dev *dev, int background)
{
	int aggressive = 0;      /* 是否激进模式 */
	int gc_ok = YAFFS_OK;    /* 垃圾回收是否成功 */
	int max_tries = 0;       /* 最大尝试次数 */
	int min_erased;          /* 最小已擦除块数 */
	int erased_chunks;       /* 已擦除chunk数 */
	int checkpt_block_adjust; /* 检查点块调整 */

	if (dev->param.gc_control_fn &&
		(dev->param.gc_control_fn(dev) & 1) == 0)
		return YAFFS_OK;  /* 垃圾回收控制函数禁止垃圾回收 */

	if (dev->gc_disable)
		/* 退出以避免递归垃圾回收 */
		return YAFFS_OK;

	/* 这个循环应该第一次就通过。
	 * 只有当回收没有增加空间时才会在这里循环。
	 */

	do {
		max_tries++;  /* 增加尝试次数 */

		checkpt_block_adjust = yaffs_calc_checkpt_blocks_required(dev);  /* 计算检查点块需求 */

		min_erased =
		    dev->param.n_reserved_blocks + checkpt_block_adjust + 1;  /* 计算最小已擦除块数 */
		erased_chunks =
		    dev->n_erased_blocks * dev->param.chunks_per_block;       /* 计算已擦除chunk数 */

		/* 如果我们很快需要一个块，那么进行激进的垃圾回收 */
		if (dev->n_erased_blocks < min_erased)
			aggressive = 1;
		else {
			if (!background
			    && erased_chunks > (dev->n_free_chunks / 4))
				break;  /* 非后台模式且已擦除chunk足够多，退出 */

			if (dev->gc_skip > 20)
				dev->gc_skip = 20;  /* 限制跳过次数 */
			if (erased_chunks < dev->n_free_chunks / 2 ||
			    dev->gc_skip < 1 || background)
				aggressive = 0;  /* 设置为非激进模式 */
			else {
				dev->gc_skip--;  /* 减少跳过次数 */
				break;
			}
		}

		dev->gc_skip = 5;  /* 重置跳过次数 */

		/* 如果我们还没有正在进行垃圾回收的块，那么看看是否
		 * 应该开始另一个 */

		if (dev->gc_block < 1 && !aggressive) {
			dev->gc_block = yaffs2_find_refresh_block(dev);  /* 查找刷新块 */
			dev->gc_chunk = 0;     /* 重置垃圾回收chunk */
			dev->n_clean_ups = 0;  /* 重置清理计数 */
		}
		if (dev->gc_block < 1) {
			dev->gc_block =
			    yaffs_find_gc_block(dev, aggressive, background);  /* 查找垃圾回收块 */
			dev->gc_chunk = 0;     /* 重置垃圾回收chunk */
			dev->n_clean_ups = 0;  /* 重置清理计数 */
		}

		if (dev->gc_block > 0) {
			dev->all_gcs++;  /* 增加总垃圾回收计数 */
			if (!aggressive)
				dev->passive_gc_count++;  /* 增加被动垃圾回收计数 */

			yaffs_trace(YAFFS_TRACE_GC,
				"yaffs: GC n_erased_blocks %d aggressive %d",
				dev->n_erased_blocks, aggressive);

			gc_ok = yaffs_gc_block(dev, dev->gc_block, aggressive);  /* 执行垃圾回收 */
		}

		if (dev->n_erased_blocks < (int)dev->param.n_reserved_blocks &&
		    dev->gc_block > 0) {
			yaffs_trace(YAFFS_TRACE_GC,
				"yaffs: GC !!!no reclaim!!! n_erased_blocks %d after try %d block %d",
				dev->n_erased_blocks, max_tries,
				dev->gc_block);
		}
	} while ((dev->n_erased_blocks < (int)dev->param.n_reserved_blocks) &&
		 (dev->gc_block > 0) && (max_tries < 2));  /* 继续直到有足够的已擦除块 */

	return aggressive ? gc_ok : YAFFS_OK;  /* 激进模式返回垃圾回收结果，否则返回OK */
}

/*
 * yaffs_bg_gc()
 * 垃圾回收。旨在从后台线程调用。
 * 如果至少一半的空闲chunk被擦除，则返回非零值。
 */
int yaffs_bg_gc(struct yaffs_dev *dev, unsigned urgency)
{
	int erased_chunks = dev->n_erased_blocks * dev->param.chunks_per_block;  /* 计算已擦除chunk数 */

	(void) urgency;  /* 未使用的参数 */
	yaffs_trace(YAFFS_TRACE_BACKGROUND, "Background gc %u", urgency);

	yaffs_check_gc(dev, 1);  /* 执行后台垃圾回收检查 */
	return erased_chunks > dev->n_free_chunks / 2;  /* 返回是否有足够的已擦除chunk */
}

/*-------------------- 数据文件操作 -----------------*/

/* 读取数据对象 */
static int yaffs_rd_data_obj(struct yaffs_obj *in, int inode_chunk, u8 * buffer)
{
	int nand_chunk = yaffs_find_chunk_in_file(in, inode_chunk, NULL);  /* 在文件中查找chunk */

	if (nand_chunk >= 0)
		return yaffs_rd_chunk_tags_nand(in->my_dev, nand_chunk,
						buffer, NULL);  /* 读取NAND chunk数据 */
	else {
		yaffs_trace(YAFFS_TRACE_NANDACCESS,
			"Chunk %d not found zero instead",
			nand_chunk);
		/* 如果读取空洞，获取合理的（零）数据 */
		memset(buffer, 0, in->my_dev->data_bytes_per_chunk);
		return 0;
	}

}

/* 删除chunk */
void yaffs_chunk_del(struct yaffs_dev *dev, int chunk_id, int mark_flash,
		     int lyn)
{
	int block;  /* 块号 */
	int page;   /* 页号 */
	struct yaffs_ext_tags tags;
	struct yaffs_block_info *bi;

	(void) lyn;  /* 未使用的参数 */
	if (chunk_id <= 0)
		return;  /* 无效的chunk ID */

	dev->n_deletions++;  /* 增加删除计数 */
	block = chunk_id / dev->param.chunks_per_block;  /* 计算块号 */
	page = chunk_id % dev->param.chunks_per_block;   /* 计算页号 */

	if (!yaffs_check_chunk_bit(dev, block, page))
		yaffs_trace(YAFFS_TRACE_VERIFY,
			"Deleting invalid chunk %d", chunk_id);

	bi = yaffs_get_block_info(dev, block);  /* 获取块信息 */

	yaffs2_update_oldest_dirty_seq(dev, block, bi);  /* 更新最旧脏序列 */

	yaffs_trace(YAFFS_TRACE_DELETION,
		"line %d delete of chunk %d",
		lyn, chunk_id);

	if (!dev->param.is_yaffs2 && mark_flash &&
	    bi->block_state != YAFFS_BLOCK_STATE_COLLECTING) {

		memset(&tags, 0, sizeof(tags));
		tags.is_deleted = 1;  /* 标记为已删除 */
		yaffs_wr_chunk_tags_nand(dev, chunk_id, NULL, &tags);  /* 写入删除标记 */
		yaffs_handle_chunk_update(dev, chunk_id, &tags);       /* 处理chunk更新 */
	} else {
		dev->n_unmarked_deletions++;  /* 增加未标记删除计数 */
	}

	/* 从管理区域中拉出。
	 * 如果整个块变脏，这将启动擦除。
	 */
	if (bi->block_state == YAFFS_BLOCK_STATE_ALLOCATING ||
	    bi->block_state == YAFFS_BLOCK_STATE_FULL ||
	    bi->block_state == YAFFS_BLOCK_STATE_NEEDS_SCAN ||
	    bi->block_state == YAFFS_BLOCK_STATE_COLLECTING) {
		dev->n_free_chunks++;                    /* 增加空闲chunk数 */
		yaffs_clear_chunk_bit(dev, block, page); /* 清除chunk位 */
		bi->pages_in_use--;                      /* 减少使用中的页数 */

		if (bi->pages_in_use == 0 &&
		    !bi->has_shrink_hdr &&
		    bi->block_state != YAFFS_BLOCK_STATE_ALLOCATING &&
		    bi->block_state != YAFFS_BLOCK_STATE_NEEDS_SCAN) {
			yaffs_block_became_dirty(dev, block);  /* 块变脏 */
		}
	}
}

/* 写数据对象 */
int yaffs_wr_data_obj(struct yaffs_obj *in, int inode_chunk,
			const u8 *buffer, int n_bytes, int use_reserve)
{
	/* 查找旧chunk，需要这样做来获取序列号
	 * 写入新的chunk并修补到树中。
	 * 使旧标签无效。
	 */

	int prev_chunk_id;               /* 前一个chunk ID */
	struct yaffs_ext_tags prev_tags; /* 前一个标签 */
	int new_chunk_id;                /* 新chunk ID */
	struct yaffs_ext_tags new_tags;  /* 新标签 */
	struct yaffs_dev *dev = in->my_dev;
	loff_t endpos;                   /* 结束位置 */

	yaffs_check_gc(dev, 0);          /* 检查垃圾回收 */

	/* 获取文件中此位置的前一个chunk（如果存在）。
	 * 如果不存在，则在树中放入零。这会现在创建tnode，
	 * 而不是稍后清理更困难时。
	 */
	prev_chunk_id = yaffs_find_chunk_in_file(in, inode_chunk, &prev_tags);
	if (prev_chunk_id < 1 &&
	    !yaffs_put_chunk_in_file(in, inode_chunk, 0, 0))
		return 0;

	/* 设置新标签 */
	memset(&new_tags, 0, sizeof(new_tags));

	new_tags.chunk_id = inode_chunk;  /* 设置chunk ID */
	new_tags.obj_id = in->obj_id;     /* 设置对象ID */
	new_tags.serial_number =
	    (prev_chunk_id > 0) ? prev_tags.serial_number + 1 : 1;  /* 设置序列号 */
	new_tags.n_bytes = n_bytes;       /* 设置字节数 */

	if (n_bytes < 1 || n_bytes > (int)dev->data_bytes_per_chunk) {
		yaffs_trace(YAFFS_TRACE_ERROR,
		  "Writing %d bytes to chunk!!!!!!!!!",
		   n_bytes);
		BUG();  /* 字节数无效 */
	}

	/*
	 * 如果这是一个数据chunk且写入超过了存储大小的末尾，
	 * 则更新stored_size。
	 */
	if (inode_chunk > 0) {
		endpos =  (inode_chunk - 1) * dev->data_bytes_per_chunk +
				n_bytes;  /* 计算结束位置 */
		if (in->variant.file_variant.stored_size < endpos)
			in->variant.file_variant.stored_size = endpos;  /* 更新存储大小 */
	}

	new_chunk_id =
	    yaffs_write_new_chunk(dev, buffer, &new_tags, use_reserve);  /* 写入新chunk */

	if (new_chunk_id > 0) {
		yaffs_put_chunk_in_file(in, inode_chunk, new_chunk_id, 0);  /* 将chunk放入文件 */

		if (prev_chunk_id > 0)
			yaffs_chunk_del(dev, prev_chunk_id, 1, __LINE__);  /* 删除旧chunk */

		yaffs_verify_file_sane(in);  /* 验证文件完整性 */
	}
	return new_chunk_id;  /* 返回新chunk ID */
}



/* 执行扩展属性修改 */
static int yaffs_do_xattrib_mod(struct yaffs_obj *obj, int set,
				const YCHAR *name, const void *value, int size,
				int flags)
{
	struct yaffs_xattr_mod xmod;  /* 扩展属性修改结构 */
	int result;

	xmod.set = set;        /* 设置操作类型（设置或删除） */
	xmod.name = name;      /* 属性名称 */
	xmod.data = value;     /* 属性值 */
	xmod.size = size;      /* 属性大小 */
	xmod.flags = flags;    /* 标志 */
	xmod.result = -ENOSPC; /* 初始结果为空间不足 */

	result = yaffs_update_oh(obj, NULL, 0, 0, 0, &xmod);  /* 更新对象头 */

	if (result > 0)
		return xmod.result;  /* 返回修改结果 */
	else
		return -ENOSPC;      /* 返回空间不足错误 */
}

/* 应用扩展属性修改 */
static int yaffs_apply_xattrib_mod(struct yaffs_obj *obj, char *buffer,
				   struct yaffs_xattr_mod *xmod)
{
	int retval = 0;
	int x_offs = sizeof(struct yaffs_obj_hdr);  /* 扩展属性偏移 */
	struct yaffs_dev *dev = obj->my_dev;
	int x_size = dev->data_bytes_per_chunk - sizeof(struct yaffs_obj_hdr);  /* 扩展属性大小 */
	char *x_buffer = buffer + x_offs;  /* 扩展属性缓冲区 */

	if (xmod->set)
		retval =
		    nval_set(dev, x_buffer, x_size, xmod->name, xmod->data,
			     xmod->size, xmod->flags);  /* 设置名称-值对 */
	else
		retval = nval_del(dev, x_buffer, x_size, xmod->name);  /* 删除名称-值对 */

	obj->has_xattr = nval_hasvalues(dev, x_buffer, x_size);  /* 检查是否有扩展属性 */
	obj->xattr_known = 1;  /* 标记扩展属性状态已知 */
	xmod->result = retval; /* 设置结果 */

	return retval;
}

/* 获取扩展属性 */
static int yaffs_do_xattrib_fetch(struct yaffs_obj *obj, const YCHAR *name,
				  void *value, int size)
{
	char *buffer = NULL;  /* 缓冲区 */
	int result;
	struct yaffs_ext_tags tags;
	struct yaffs_dev *dev = obj->my_dev;
	int x_offs = sizeof(struct yaffs_obj_hdr);  /* 扩展属性偏移 */
	int x_size = dev->data_bytes_per_chunk - sizeof(struct yaffs_obj_hdr);  /* 扩展属性大小 */
	char *x_buffer;  /* 扩展属性缓冲区 */
	int retval = 0;

	if (obj->hdr_chunk < 1)
		return -ENODATA;  /* 没有头chunk */

	/* 如果我们知道对象没有扩展属性，那么不要进行所有的
	 * 读取和解析。
	 */
	if (obj->xattr_known && !obj->has_xattr) {
		if (name)
			return -ENODATA;  /* 没有数据 */
		else
			return 0;         /* 返回0表示没有属性 */
	}

	buffer = (char *)yaffs_get_temp_buffer(dev);  /* 获取临时缓冲区 */
	if (!buffer)
		return -ENOMEM;  /* 内存不足 */

	result =
	    yaffs_rd_chunk_tags_nand(dev, obj->hdr_chunk, (u8 *) buffer, &tags);  /* 读取头chunk */

	if (result != YAFFS_OK)
		retval = -ENOENT;  /* 条目不存在 */
	else {
		x_buffer = buffer + x_offs;  /* 指向扩展属性区域 */

		if (!obj->xattr_known) {
			obj->has_xattr = nval_hasvalues(dev, x_buffer, x_size);  /* 检查是否有扩展属性 */
			obj->xattr_known = 1;  /* 标记扩展属性状态已知 */
		}

		if (name)
			retval = nval_get(dev, x_buffer, x_size,
						name, value, size);  /* 获取指定属性 */
		else
			retval = nval_list(dev, x_buffer, x_size, value, size);  /* 列出所有属性 */
	}
	yaffs_release_temp_buffer(dev, (u8 *) buffer);  /* 释放临时缓冲区 */
	return retval;
}

/* 设置扩展属性 */
int yaffs_set_xattrib(struct yaffs_obj *obj, const YCHAR * name,
		      const void *value, int size, int flags)
{
	return yaffs_do_xattrib_mod(obj, 1, name, value, size, flags);
}

/* 移除扩展属性 */
int yaffs_remove_xattrib(struct yaffs_obj *obj, const YCHAR * name)
{
	return yaffs_do_xattrib_mod(obj, 0, name, NULL, 0, 0);
}

/* 获取扩展属性 */
int yaffs_get_xattrib(struct yaffs_obj *obj, const YCHAR * name, void *value,
		      int size)
{
	return yaffs_do_xattrib_fetch(obj, name, value, size);
}

/* 列出扩展属性 */
int yaffs_list_xattrib(struct yaffs_obj *obj, char *buffer, int size)
{
	return yaffs_do_xattrib_fetch(obj, NULL, buffer, size);
}

/* 检查对象详情是否已加载 */
static void yaffs_check_obj_details_loaded(struct yaffs_obj *in)
{
	u8 *buf;                     /* 缓冲区 */
	struct yaffs_obj_hdr *oh;    /* 对象头 */
	struct yaffs_dev *dev;       /* 设备 */
	struct yaffs_ext_tags tags;  /* 扩展标签 */
	int result;

	if (!in || !in->lazy_loaded || in->hdr_chunk < 1)
		return;  /* 对象无效或已加载或没有头chunk */

	dev = in->my_dev;
	buf = yaffs_get_temp_buffer(dev);  /* 获取临时缓冲区 */

	result = yaffs_rd_chunk_tags_nand(dev, in->hdr_chunk, buf, &tags);  /* 读取头chunk */

	if (result == YAFFS_FAIL) {
		yaffs_release_temp_buffer(dev, buf);  /* 释放缓冲区 */
		return;
	}

	oh = (struct yaffs_obj_hdr *)buf;  /* 指向对象头 */

	yaffs_do_endian_oh(dev, oh);  /* 处理字节序 */

	in->lazy_loaded = 0;                        /* 清除延迟加载标志 */
	in->yst_mode = oh->yst_mode;                /* 设置模式 */
	yaffs_load_attribs(in, oh);                 /* 加载属性 */
	yaffs_set_obj_name_from_oh(in, oh);         /* 从对象头设置名称 */

	if (in->variant_type == YAFFS_OBJECT_TYPE_SYMLINK)
		in->variant.symlink_variant.alias =
		    yaffs_clone_str(oh->alias);  /* 克隆符号链接别名 */
	yaffs_release_temp_buffer(dev, buf);  /* 释放临时缓冲区 */
}

/* UpdateObjectHeader更新NAND上对象的头部。
 * 如果name不为NULL，则使用该新名称。
 *
 * 我们总是从头开始创建对象头（除了读取旧名称），
 * 所以首先以CPU字节序设置，然后在最后进行字节序修复。
 *
 * 但是，有一个转折：如果有扩展属性，我们保持它们原样。
 *
 * 小心！缓冲区保存整个chunk。chunk的一部分保存对象头，
 * 其余部分保存扩展属性，因此我们使用缓冲区指针和oh指针
 * 指向同一内存。
 */

int yaffs_update_oh(struct yaffs_obj *in, const YCHAR *name, int force,
		    int is_shrink, int shadows, struct yaffs_xattr_mod *xmod)
{

	struct yaffs_block_info *bi;        /* 块信息 */
	struct yaffs_dev *dev = in->my_dev;  /* 设备 */
	int prev_chunk_id;                  /* 前一个chunk ID */
	int ret_val = 0;                    /* 返回值 */
	int result = 0;                     /* 结果 */
	int new_chunk_id;                   /* 新chunk ID */
	struct yaffs_ext_tags new_tags;     /* 新标签 */
	struct yaffs_ext_tags old_tags;     /* 旧标签 */
	const YCHAR *alias = NULL;          /* 别名 */
	u8 *buffer = NULL;                  /* 缓冲区 */
	YCHAR old_name[YAFFS_MAX_NAME_LENGTH + 1];  /* 旧名称 */
	struct yaffs_obj_hdr *oh = NULL;    /* 对象头 */
	loff_t file_size = 0;               /* 文件大小 */

	strcpy(old_name, _Y("silly old name"));  /* 初始化旧名称 */

	if (in->fake && in != dev->root_dir && !force && !xmod)
		return ret_val;  /* 虚假对象且非根目录且非强制且无扩展属性修改 */

	yaffs_check_gc(dev, 0);                  /* 检查垃圾回收 */
	yaffs_check_obj_details_loaded(in);      /* 检查对象详情是否已加载 */

	buffer = yaffs_get_temp_buffer(in->my_dev);  /* 获取临时缓冲区 */
	oh = (struct yaffs_obj_hdr *)buffer;         /* 指向对象头 */

	prev_chunk_id = in->hdr_chunk;  /* 获取前一个头chunk ID */

	if (prev_chunk_id > 0) {
		/* 访问旧对象头只是为了读取名称 */
		result = yaffs_rd_chunk_tags_nand(dev, prev_chunk_id,
						  buffer, &old_tags);
		if (result == YAFFS_OK) {
			yaffs_verify_oh(in, oh, &old_tags, 0);  /* 验证对象头 */
			memcpy(old_name, oh->name, sizeof(oh->name));  /* 复制旧名称 */

			/*
			* 注意：我们只清除对象头区域，因为缓冲区的其余部分
			* 可能包含扩展属性。
			*/
			memset(oh, 0xff, sizeof(*oh));  /* 清除对象头 */
		}
	} else {
		memset(buffer, 0xff, dev->data_bytes_per_chunk);  /* 清除整个缓冲区 */
	}

	oh->type = in->variant_type;                              /* 设置对象类型 */
	oh->yst_mode = in->yst_mode;                              /* 设置模式 */
	oh->shadows_obj = oh->inband_shadowed_obj_id = shadows;   /* 设置阴影对象 */

	yaffs_load_attribs_oh(oh, in);  /* 加载属性到对象头 */

	if (in->parent)
		oh->parent_obj_id = in->parent->obj_id;  /* 设置父对象ID */
	else
		oh->parent_obj_id = 0;                   /* 没有父对象 */

	if (name && *name) {
		memset(oh->name, 0, sizeof(oh->name));           /* 清零名称 */
		yaffs_load_oh_from_name(dev, oh->name, name);    /* 从名称加载到对象头 */
	} else if (prev_chunk_id > 0) {
		memcpy(oh->name, old_name, sizeof(oh->name));    /* 使用旧名称 */
	} else {
		memset(oh->name, 0, sizeof(oh->name));           /* 清零名称 */
	}

	oh->is_shrink = is_shrink;  /* 设置收缩标志 */

	switch (in->variant_type) {
	case YAFFS_OBJECT_TYPE_UNKNOWN:
		/* 不应该发生 */
		break;
	case YAFFS_OBJECT_TYPE_FILE:
		if (oh->parent_obj_id != YAFFS_OBJECTID_DELETED &&
		    oh->parent_obj_id != YAFFS_OBJECTID_UNLINKED)
			file_size = in->variant.file_variant.stored_size;  /* 获取存储大小 */
		yaffs_oh_size_load(dev, oh, file_size, 0);  /* 加载文件大小到对象头 */
		break;
	case YAFFS_OBJECT_TYPE_HARDLINK:
		oh->equiv_id = in->variant.hardlink_variant.equiv_id;  /* 设置等价ID */
		break;
	case YAFFS_OBJECT_TYPE_SPECIAL:
		/* 无需操作 */
		break;
	case YAFFS_OBJECT_TYPE_DIRECTORY:
		/* 无需操作 */
		break;
	case YAFFS_OBJECT_TYPE_SYMLINK:
		alias = in->variant.symlink_variant.alias;  /* 获取符号链接别名 */
		if (!alias)
			alias = _Y("no alias");  /* 默认别名 */
		strncpy(oh->alias, alias, YAFFS_MAX_ALIAS_LENGTH);  /* 复制别名 */
		oh->alias[YAFFS_MAX_ALIAS_LENGTH] = 0;              /* 确保字符串结束 */
		break;
	}

	/* 处理任何扩展属性修改 */
	if (xmod)
		yaffs_apply_xattrib_mod(in, (char *)buffer, xmod);

	/* 标签 */
	memset(&new_tags, 0, sizeof(new_tags));  /* 清零新标签 */
	in->serial++;                            /* 增加序列号 */
	new_tags.chunk_id = 0;                   /* chunk ID为0（对象头） */
	new_tags.obj_id = in->obj_id;            /* 设置对象ID */
	new_tags.serial_number = in->serial;     /* 设置序列号 */

	/* 为文件头添加额外信息 */
	new_tags.extra_available = 1;                                /* 额外信息可用 */
	new_tags.extra_parent_id = oh->parent_obj_id;                /* 父对象ID */
	new_tags.extra_file_size = file_size;                        /* 文件大小 */
	new_tags.extra_is_shrink = oh->is_shrink;                    /* 收缩标志 */
	new_tags.extra_equiv_id = oh->equiv_id;                      /* 等价ID */
	new_tags.extra_shadows = (oh->shadows_obj > 0) ? 1 : 0;      /* 阴影标志 */
	new_tags.extra_obj_type = in->variant_type;                  /* 对象类型 */

	/* 现在如果需要，对对象头进行字节序转换 */
	yaffs_do_endian_oh(dev, oh);

	yaffs_verify_oh(in, oh, &new_tags, 1);  /* 验证对象头 */

	/* 在NAND中创建新chunk */
	new_chunk_id =
	    yaffs_write_new_chunk(dev, buffer, &new_tags,
				  (prev_chunk_id > 0) ? 1 : 0);

	if (buffer)
		yaffs_release_temp_buffer(dev, buffer);  /* 释放临时缓冲区 */

	if (new_chunk_id < 0)
		return new_chunk_id;  /* 写入失败 */

	in->hdr_chunk = new_chunk_id;  /* 设置新的头chunk */

	if (prev_chunk_id > 0)
		yaffs_chunk_del(dev, prev_chunk_id, 1, __LINE__);  /* 删除旧的头chunk */

	if (!yaffs_obj_cache_dirty(in))
		in->dirty = 0;  /* 如果缓存不脏，清除脏标志 */

	/* 如果这是一个收缩操作，则标记chunk所在的块 */
	if (is_shrink) {
		bi = yaffs_get_block_info(in->my_dev,
					  new_chunk_id /
					  in->my_dev->param.chunks_per_block);
		bi->has_shrink_hdr = 1;  /* 标记块有收缩头 */
	}

	return new_chunk_id;  /* 返回新chunk ID */
}

/*--------------------- 文件读写 ------------------------
 * 读和写有非常相似的结构。
 * 一般来说，读/写有三个部分：
 * 开始的不完整chunk（如果读/写不是chunk对齐的）
 * 一些完整的chunks
 * 结束的不完整chunk
 *
 * 特殊情况：第一个chunk可能也是最后一个chunk。
 */

/* 文件读取 */
int yaffs_file_rd(struct yaffs_obj *in, u8 * buffer, loff_t offset, int n_bytes)
{
	int chunk;                    /* chunk编号 */
	u32 start;                    /* 起始偏移 */
	int n_copy;                   /* 要复制的字节数 */
	int n = n_bytes;              /* 剩余字节数 */
	int n_done = 0;               /* 已完成字节数 */
	struct yaffs_cache *cache;    /* 缓存 */
	struct yaffs_dev *dev;        /* 设备 */

	dev = in->my_dev;

	while (n > 0) {
		yaffs_addr_to_chunk(dev, offset, &chunk, &start);  /* 将地址转换为chunk和偏移 */
		chunk++;  /* chunk编号从1开始 */

		/* 检查特殊情况：开始和结束在同一个chunk中 */
		if ((start + n) < dev->data_bytes_per_chunk)
			n_copy = n;  /* 全部数据在一个chunk中 */
		else
			n_copy = dev->data_bytes_per_chunk - start;  /* 只复制到chunk末尾 */

		cache = yaffs_find_chunk_cache(in, chunk);  /* 查找chunk缓存 */

		/* 如果chunk已经在缓存中，或者小于一个完整chunk，
		 * 或者我们使用带内标签，则使用缓存（如果有缓存），
		 * 否则绕过缓存。
		 */
		if (cache || n_copy != (int)dev->data_bytes_per_chunk ||
		    dev->param.inband_tags) {
			if (dev->param.n_caches > 0) {

				/* 如果在缓存中找不到数据，则加载它 */

				if (!cache) {
					cache =
					    yaffs_grab_chunk_cache(in->my_dev);  /* 获取chunk缓存 */
					cache->object = in;         /* 设置对象 */
					cache->chunk_id = chunk;    /* 设置chunk ID */
					cache->dirty = 0;           /* 不脏 */
					cache->locked = 0;          /* 未锁定 */
					yaffs_rd_data_obj(in, chunk,
							  cache->data);  /* 读取数据到缓存 */
					cache->n_bytes = 0;         /* 字节数为0 */
				}

				yaffs_use_cache(dev, cache, 0);  /* 使用缓存 */

				cache->locked = 1;  /* 锁定缓存 */

				memcpy(buffer, &cache->data[start], n_copy);  /* 从缓存复制数据 */

				cache->locked = 0;  /* 解锁缓存 */
			} else {
				/* 读取到本地缓冲区然后复制... */

				u8 *local_buffer =
				    yaffs_get_temp_buffer(dev);  /* 获取临时缓冲区 */
				yaffs_rd_data_obj(in, chunk, local_buffer);  /* 读取数据到本地缓冲区 */

				memcpy(buffer, &local_buffer[start], n_copy);  /* 复制数据 */

				yaffs_release_temp_buffer(dev, local_buffer);  /* 释放临时缓冲区 */
			}
		} else {
			/* 完整的chunk。直接读取到缓冲区。 */
			yaffs_rd_data_obj(in, chunk, buffer);
		}
		n -= n_copy;        /* 减少剩余字节数 */
		offset += n_copy;   /* 增加偏移量 */
		buffer += n_copy;   /* 移动缓冲区指针 */
		n_done += n_copy;   /* 增加已完成字节数 */
	}
	return n_done;  /* 返回已读取的字节数 */
}

/* 执行文件写入 */
int yaffs_do_file_wr(struct yaffs_obj *in, const u8 *buffer, loff_t offset,
		     int n_bytes, int write_through)
{

	int chunk;                    /* chunk编号 */
	u32 start;                    /* 起始偏移 */
	int n_copy;                   /* 要复制的字节数 */
	int n = n_bytes;              /* 剩余字节数 */
	int n_done = 0;               /* 已完成字节数 */
	int n_writeback;              /* 需要写回的字节数 */
	loff_t start_write = offset;  /* 开始写入的偏移 */
	int chunk_written = 0;        /* 写入的chunk数 */
	u32 n_bytes_read;             /* 读取的字节数 */
	loff_t chunk_start;           /* chunk开始位置 */
	struct yaffs_dev *dev;        /* 设备 */

	dev = in->my_dev;

	while (n > 0 && chunk_written >= 0) {
		yaffs_addr_to_chunk(dev, offset, &chunk, &start);  /* 将地址转换为chunk和偏移 */

		if (((loff_t)chunk) *
		    dev->data_bytes_per_chunk + start != offset ||
		    start >= dev->data_bytes_per_chunk) {
			yaffs_trace(YAFFS_TRACE_ERROR,
				"AddrToChunk of offset %lld gives chunk %d start %d",
				(long  long)offset, chunk, start);
		}
		chunk++;	/* 文件位置转换为文件中的chunk偏移 */

		/* 检查特殊情况：开始和结束在同一个chunk中 */

		if ((start + n) < dev->data_bytes_per_chunk) {
			n_copy = n;  /* 全部数据在一个chunk中 */

			/* 现在计算需要写回多少字节...
			 * 如果我们正在覆写且不是写到文件末尾，
			 * 那么我们需要写回之前存在的数据量。
			 */

			chunk_start = (((loff_t)(chunk - 1)) *
					dev->data_bytes_per_chunk);  /* 计算chunk开始位置 */

			if (chunk_start > in->variant.file_variant.file_size)
				n_bytes_read = 0;	/* 超过文件末尾 */
			else
				n_bytes_read =
				    in->variant.file_variant.file_size -
				    chunk_start;  /* 计算需要读取的字节数 */

			if (n_bytes_read > dev->data_bytes_per_chunk)
				n_bytes_read = dev->data_bytes_per_chunk;  /* 限制在chunk大小内 */

			n_writeback =
			    (n_bytes_read >
			     (start + n)) ? n_bytes_read : (start + n);  /* 计算写回字节数 */

			if (n_writeback < 0 ||
			    n_writeback > (int)dev->data_bytes_per_chunk)
				BUG();  /* 写回字节数无效 */

		} else {
			n_copy = dev->data_bytes_per_chunk - start;  /* 复制到chunk末尾 */
			n_writeback = dev->data_bytes_per_chunk;     /* 写回整个chunk */
		}

		if (n_copy != (int)dev->data_bytes_per_chunk ||
		    !dev->param.cache_bypass_aligned ||
		    dev->param.inband_tags) {
			/* 不完整的开始或结束chunk（或者开始和结束都是chunk），
			 * 或者我们使用带内标签，或者我们强制通过缓存写入，
			 * 所以我们想使用缓存缓冲区。
			 */
			if (dev->param.n_caches > 0) {
				struct yaffs_cache *cache;

				/* If we can't find the data in the cache, then
				 * load the cache */
				cache = yaffs_find_chunk_cache(in, chunk);

				if (!cache &&
				    yaffs_check_alloc_available(dev, 1)) {
					cache = yaffs_grab_chunk_cache(dev);
					cache->object = in;
					cache->chunk_id = chunk;
					cache->dirty = 0;
					cache->locked = 0;
					yaffs_rd_data_obj(in, chunk,
							  cache->data);
				} else if (cache &&
					   !cache->dirty &&
					   !yaffs_check_alloc_available(dev,
									1)) {
					/* Drop the cache if it was a read cache
					 * item and no space check has been made
					 * for it.
					 */
					cache = NULL;
				}

				if (cache) {
					yaffs_use_cache(dev, cache, 1);
					cache->locked = 1;

					memcpy(&cache->data[start], buffer,
					       n_copy);

					cache->locked = 0;
					cache->n_bytes = n_writeback;

					if (write_through) {
						chunk_written =
						    yaffs_wr_data_obj
						    (cache->object,
						     cache->chunk_id,
						     cache->data,
						     cache->n_bytes, 1);
						cache->dirty = 0;
					}
				} else {
					chunk_written = -1;	/* fail write */
				}
			} else {
				/* An incomplete start or end chunk (or maybe
				 * both start and end chunk). Read into the
				 * local buffer then copy over and write back.
				 */

				u8 *local_buffer = yaffs_get_temp_buffer(dev);

				yaffs_rd_data_obj(in, chunk, local_buffer);
				memcpy(&local_buffer[start], buffer, n_copy);

				chunk_written =
				    yaffs_wr_data_obj(in, chunk,
						      local_buffer,
						      n_writeback, 0);

				yaffs_release_temp_buffer(dev, local_buffer);
			}
		} else {
			/* A full chunk. Write directly from the buffer. */

			chunk_written =
			    yaffs_wr_data_obj(in, chunk, buffer,
					      dev->data_bytes_per_chunk, 0);

			/* Since we've overwritten the cached data,
			 * we better invalidate it. */
			yaffs_invalidate_chunk_cache(in, chunk);
		}

		if (chunk_written >= 0) {
			n -= n_copy;
			offset += n_copy;
			buffer += n_copy;
			n_done += n_copy;
		}
	}

	/* Update file object */

	if ((start_write + n_done) > in->variant.file_variant.file_size)
		in->variant.file_variant.file_size = (start_write + n_done);

	in->dirty = 1;
	return n_done;
}

int yaffs_wr_file(struct yaffs_obj *in, const u8 *buffer, loff_t offset,
		  int n_bytes, int write_through)
{
	yaffs2_handle_hole(in, offset);
	return yaffs_do_file_wr(in, buffer, offset, n_bytes, write_through);
}

/* ---------------------- File resizing stuff ------------------ */

static void yaffs_prune_chunks(struct yaffs_obj *in, loff_t new_size)
{

	struct yaffs_dev *dev = in->my_dev;
	loff_t old_size = in->variant.file_variant.file_size;
	int i;
	int chunk_id;
	u32 dummy;
	int last_del;
	int start_del;

	if (old_size > 0)
		yaffs_addr_to_chunk(dev, old_size - 1, &last_del, &dummy);
	else
		last_del = 0;

	yaffs_addr_to_chunk(dev, new_size + dev->data_bytes_per_chunk - 1,
				&start_del, &dummy);
	last_del++;
	start_del++;

	/* Delete backwards so that we don't end up with holes if
	 * power is lost part-way through the operation.
	 */
	for (i = last_del; i >= start_del; i--) {
		/* NB this could be optimised somewhat,
		 * eg. could retrieve the tags and write them without
		 * using yaffs_chunk_del
		 */

		chunk_id = yaffs_find_del_file_chunk(in, i, NULL);

		if (chunk_id < 1)
			continue;

		if ((u32)chunk_id <
		    (dev->internal_start_block * dev->param.chunks_per_block) ||
		    (u32)chunk_id >=
		    ((dev->internal_end_block + 1) *
		      dev->param.chunks_per_block)) {
			yaffs_trace(YAFFS_TRACE_ALWAYS,
				"Found daft chunk_id %d for %d",
				chunk_id, i);
		} else {
			in->n_data_chunks--;
			yaffs_chunk_del(dev, chunk_id, 1, __LINE__);
		}
	}
}

void yaffs_resize_file_down(struct yaffs_obj *obj, loff_t new_size)
{
	int new_full;
	u32 new_partial;
	struct yaffs_dev *dev = obj->my_dev;

	yaffs_addr_to_chunk(dev, new_size, &new_full, &new_partial);

	yaffs_prune_chunks(obj, new_size);

	if (new_partial != 0) {
		int last_chunk = 1 + new_full;
		u8 *local_buffer = yaffs_get_temp_buffer(dev);

		/* Rewrite the last chunk with its new size and zero pad */
		yaffs_rd_data_obj(obj, last_chunk, local_buffer);
		memset(local_buffer + new_partial, 0,
		       dev->data_bytes_per_chunk - new_partial);

		yaffs_wr_data_obj(obj, last_chunk, local_buffer,
				  new_partial, 1);

		yaffs_release_temp_buffer(dev, local_buffer);
	}

	obj->variant.file_variant.file_size = new_size;
	obj->variant.file_variant.stored_size = new_size;

	yaffs_prune_tree(dev, &obj->variant.file_variant);
}

int yaffs_resize_file(struct yaffs_obj *in, loff_t new_size)
{
	struct yaffs_dev *dev = in->my_dev;
	loff_t old_size = in->variant.file_variant.file_size;

	yaffs_flush_file_cache(in, 1);
	yaffs_invalidate_file_cache(in);

	yaffs_check_gc(dev, 0);

	if (in->variant_type != YAFFS_OBJECT_TYPE_FILE)
		return YAFFS_FAIL;

	if (new_size == old_size)
		return YAFFS_OK;

	if (new_size > old_size) {
		yaffs2_handle_hole(in, new_size);
		in->variant.file_variant.file_size = new_size;
	} else {
		/* new_size < old_size */
		yaffs_resize_file_down(in, new_size);
	}

	/* Write a new object header to reflect the resize.
	 * show we've shrunk the file, if need be
	 * Do this only if the file is not in the deleted directories
	 * and is not shadowed.
	 */
	if (in->parent &&
	    !in->is_shadowed &&
	    in->parent->obj_id != YAFFS_OBJECTID_UNLINKED &&
	    in->parent->obj_id != YAFFS_OBJECTID_DELETED)
		yaffs_update_oh(in, NULL, 0, 0, 0, NULL);

	return YAFFS_OK;
}

int yaffs_flush_file(struct yaffs_obj *in,
		     int update_time,
		     int data_sync,
		     int discard_cache)
{
	if (!in->dirty)
		return YAFFS_OK;

	yaffs_flush_file_cache(in, discard_cache);

	if (data_sync)
		return YAFFS_OK;

	if (update_time)
		yaffs_load_current_time(in, 0, 0);

	return (yaffs_update_oh(in, NULL, 0, 0, 0, NULL) >= 0) ?
				YAFFS_OK : YAFFS_FAIL;
}


/* yaffs_del_file deletes the whole file data
 * and the inode associated with the file.
 * It does not delete the links associated with the file.
 */
static int yaffs_unlink_file_if_needed(struct yaffs_obj *in)
{
	int ret_val;
	int del_now = 0;
	struct yaffs_dev *dev = in->my_dev;

	if (!in->my_inode)
		del_now = 1;

	if (del_now) {
		ret_val =
		    yaffs_change_obj_name(in, in->my_dev->del_dir,
					  _Y("deleted"), 0, 0);
		yaffs_trace(YAFFS_TRACE_TRACING,
			"yaffs: immediate deletion of file %d",
			in->obj_id);
		in->deleted = 1;
		in->my_dev->n_deleted_files++;
		if (dev->param.disable_soft_del || dev->param.is_yaffs2)
			yaffs_resize_file(in, 0);
		yaffs_soft_del_file(in);
	} else {
		ret_val =
		    yaffs_change_obj_name(in, in->my_dev->unlinked_dir,
					  _Y("unlinked"), 0, 0);
	}
	return ret_val;
}

static int yaffs_del_file(struct yaffs_obj *in)
{
	int ret_val = YAFFS_OK;
	int deleted;	/* Need to cache value on stack if in is freed */
	struct yaffs_dev *dev = in->my_dev;

	if (dev->param.disable_soft_del || dev->param.is_yaffs2)
		yaffs_resize_file(in, 0);

	if (in->n_data_chunks > 0) {
		/* Use soft deletion if there is data in the file.
		 * That won't be the case if it has been resized to zero.
		 */
		if (!in->unlinked)
			ret_val = yaffs_unlink_file_if_needed(in);

		deleted = in->deleted;

		if (ret_val == YAFFS_OK && in->unlinked && !in->deleted) {
			in->deleted = 1;
			deleted = 1;
			in->my_dev->n_deleted_files++;
			yaffs_soft_del_file(in);
		}
		return deleted ? YAFFS_OK : YAFFS_FAIL;
	} else {
		/* The file has no data chunks so we toss it immediately */
		yaffs_free_tnode(in->my_dev, in->variant.file_variant.top);
		in->variant.file_variant.top = NULL;
		yaffs_generic_obj_del(in);

		return YAFFS_OK;
	}
}

int yaffs_is_non_empty_dir(struct yaffs_obj *obj)
{
	return (obj &&
		obj->variant_type == YAFFS_OBJECT_TYPE_DIRECTORY) &&
		!(list_empty(&obj->variant.dir_variant.children));
}

static int yaffs_del_dir(struct yaffs_obj *obj)
{
	/* First check that the directory is empty. */
	if (yaffs_is_non_empty_dir(obj))
		return YAFFS_FAIL;

	return yaffs_generic_obj_del(obj);
}

static int yaffs_del_symlink(struct yaffs_obj *in)
{
	kfree(in->variant.symlink_variant.alias);
	in->variant.symlink_variant.alias = NULL;

	return yaffs_generic_obj_del(in);
}

static int yaffs_del_link(struct yaffs_obj *in)
{
	/* remove this hardlink from the list associated with the equivalent
	 * object
	 */
	list_del_init(&in->hard_links);
	return yaffs_generic_obj_del(in);
}

int yaffs_del_obj(struct yaffs_obj *obj)
{
	int ret_val = -1;

	switch (obj->variant_type) {
	case YAFFS_OBJECT_TYPE_FILE:
		ret_val = yaffs_del_file(obj);
		break;
	case YAFFS_OBJECT_TYPE_DIRECTORY:
		if (!list_empty(&obj->variant.dir_variant.dirty)) {
			yaffs_trace(YAFFS_TRACE_BACKGROUND,
				"Remove object %d from dirty directories",
				obj->obj_id);
			list_del_init(&obj->variant.dir_variant.dirty);
		}
		return yaffs_del_dir(obj);
		break;
	case YAFFS_OBJECT_TYPE_SYMLINK:
		ret_val = yaffs_del_symlink(obj);
		break;
	case YAFFS_OBJECT_TYPE_HARDLINK:
		ret_val = yaffs_del_link(obj);
		break;
	case YAFFS_OBJECT_TYPE_SPECIAL:
		ret_val = yaffs_generic_obj_del(obj);
		break;
	case YAFFS_OBJECT_TYPE_UNKNOWN:
		ret_val = 0;
		break;		/* should not happen. */
	}
	return ret_val;
}


static void yaffs_empty_dir_to_dir(struct yaffs_obj *from_dir,
				   struct yaffs_obj *to_dir)
{
	struct yaffs_obj *obj;
	struct list_head *lh;
	struct list_head *n;

	list_for_each_safe(lh, n, &from_dir->variant.dir_variant.children) {
		obj = list_entry(lh, struct yaffs_obj, siblings);
		yaffs_add_obj_to_dir(to_dir, obj);
	}
}

struct yaffs_obj *yaffs_retype_obj(struct yaffs_obj *obj,
				   enum yaffs_obj_type type)
{
	/* Tear down the old variant */
	switch (obj->variant_type) {
	case YAFFS_OBJECT_TYPE_FILE:
		/* Nuke file data */
		yaffs_resize_file(obj, 0);
		yaffs_free_tnode(obj->my_dev, obj->variant.file_variant.top);
		obj->variant.file_variant.top = NULL;
		break;
	case YAFFS_OBJECT_TYPE_DIRECTORY:
		/* Put the children in lost and found. */
		yaffs_empty_dir_to_dir(obj, obj->my_dev->lost_n_found);
		if (!list_empty(&obj->variant.dir_variant.dirty))
			list_del_init(&obj->variant.dir_variant.dirty);
		break;
	case YAFFS_OBJECT_TYPE_SYMLINK:
		/* Nuke symplink data */
		kfree(obj->variant.symlink_variant.alias);
		obj->variant.symlink_variant.alias = NULL;
		break;
	case YAFFS_OBJECT_TYPE_HARDLINK:
		list_del_init(&obj->hard_links);
		break;
	default:
		break;
	}

	memset(&obj->variant, 0, sizeof(obj->variant));

	/*Set up new variant if the memset is not enough. */
	switch (type) {
	case YAFFS_OBJECT_TYPE_DIRECTORY:
		INIT_LIST_HEAD(&obj->variant.dir_variant.children);
		INIT_LIST_HEAD(&obj->variant.dir_variant.dirty);
		break;
	case YAFFS_OBJECT_TYPE_FILE:
	case YAFFS_OBJECT_TYPE_SYMLINK:
	case YAFFS_OBJECT_TYPE_HARDLINK:
	default:
		break;
	}

	obj->variant_type = type;

	return obj;

}

static int yaffs_unlink_worker(struct yaffs_obj *obj)
{
	int del_now = 0;

	if (!obj)
		return YAFFS_FAIL;

	if (!obj->my_inode)
		del_now = 1;

	yaffs_update_parent(obj->parent);

	if (obj->variant_type == YAFFS_OBJECT_TYPE_HARDLINK) {
		return yaffs_del_link(obj);
	} else if (!list_empty(&obj->hard_links)) {
		/* Curve ball: We're unlinking an object that has a hardlink.
		 *
		 * This problem arises because we are not strictly following
		 * The Linux link/inode model.
		 *
		 * We can't really delete the object.
		 * Instead, we do the following:
		 * - Select a hardlink.
		 * - Unhook it from the hard links
		 * - Move it from its parent directory so that the rename works.
		 * - Rename the object to the hardlink's name.
		 * - Delete the hardlink
		 */

		struct yaffs_obj *hl;
		struct yaffs_obj *parent;
		int ret_val;
		YCHAR name[YAFFS_MAX_NAME_LENGTH + 1];

		hl = list_entry(obj->hard_links.next, struct yaffs_obj,
				hard_links);

		yaffs_get_obj_name(hl, name, YAFFS_MAX_NAME_LENGTH + 1);
		parent = hl->parent;

		list_del_init(&hl->hard_links);

		yaffs_add_obj_to_dir(obj->my_dev->unlinked_dir, hl);

		ret_val = yaffs_change_obj_name(obj, parent, name, 0, 0);

		if (ret_val == YAFFS_OK)
			ret_val = yaffs_generic_obj_del(hl);

		return ret_val;

	} else if (del_now) {
		switch (obj->variant_type) {
		case YAFFS_OBJECT_TYPE_FILE:
			return yaffs_del_file(obj);
			break;
		case YAFFS_OBJECT_TYPE_DIRECTORY:
			list_del_init(&obj->variant.dir_variant.dirty);
			return yaffs_del_dir(obj);
			break;
		case YAFFS_OBJECT_TYPE_SYMLINK:
			return yaffs_del_symlink(obj);
			break;
		case YAFFS_OBJECT_TYPE_SPECIAL:
			return yaffs_generic_obj_del(obj);
			break;
		case YAFFS_OBJECT_TYPE_HARDLINK:
		case YAFFS_OBJECT_TYPE_UNKNOWN:
		default:
			return YAFFS_FAIL;
		}
	} else if (yaffs_is_non_empty_dir(obj)) {
		return YAFFS_FAIL;
	} else {
		return yaffs_change_obj_name(obj, obj->my_dev->unlinked_dir,
						_Y("unlinked"), 0, 0);
	}
}

int yaffs_unlink_obj(struct yaffs_obj *obj)
{
	if (obj && obj->unlink_allowed)
		return yaffs_unlink_worker(obj);

	return YAFFS_FAIL;
}

int yaffs_unlinker(struct yaffs_obj *dir, const YCHAR *name)
{
	struct yaffs_obj *obj;

	obj = yaffs_find_by_name(dir, name);
	return yaffs_unlink_obj(obj);
}

/* Note:
 * If old_name is NULL then we take old_dir as the object to be renamed.
 */
int yaffs_rename_obj(struct yaffs_obj *old_dir, const YCHAR *old_name,
		     struct yaffs_obj *new_dir, const YCHAR *new_name)
{
	struct yaffs_obj *obj = NULL;
	struct yaffs_obj *existing_target = NULL;
	int force = 0;
	int result;
	struct yaffs_dev *dev;

	if (!old_dir || old_dir->variant_type != YAFFS_OBJECT_TYPE_DIRECTORY) {
		BUG();
		return YAFFS_FAIL;
	}
	if (!new_dir || new_dir->variant_type != YAFFS_OBJECT_TYPE_DIRECTORY) {
		BUG();
		return YAFFS_FAIL;
	}

	dev = old_dir->my_dev;

#ifdef CONFIG_YAFFS_CASE_INSENSITIVE
	/* Special case for case insemsitive systems.
	 * While look-up is case insensitive, the name isn't.
	 * Therefore we might want to change x.txt to X.txt
	 */
	if (old_dir == new_dir &&
		old_name && new_name &&
		strcmp(old_name, new_name) == 0)
		force = 1;
#endif

	if (strnlen(new_name, YAFFS_MAX_NAME_LENGTH + 1) >
	    YAFFS_MAX_NAME_LENGTH)
		/* ENAMETOOLONG */
		return YAFFS_FAIL;

	if (old_name)
		obj = yaffs_find_by_name(old_dir, old_name);
	else{
		obj = old_dir;
		old_dir = obj->parent;
	}

	if (obj && obj->rename_allowed) {
		/* Now handle an existing target, if there is one */
		existing_target = yaffs_find_by_name(new_dir, new_name);
		if (yaffs_is_non_empty_dir(existing_target)) {
			return YAFFS_FAIL;	/* ENOTEMPTY */
		} else if (existing_target && existing_target != obj) {
			/* Nuke the target first, using shadowing,
			 * but only if it isn't the same object.
			 *
			 * Note we must disable gc here otherwise it can mess
			 * up the shadowing.
			 *
			 */
			dev->gc_disable = 1;
			yaffs_change_obj_name(obj, new_dir, new_name, force,
					      existing_target->obj_id);
			existing_target->is_shadowed = 1;
			yaffs_unlink_obj(existing_target);
			dev->gc_disable = 0;
		}

		result = yaffs_change_obj_name(obj, new_dir, new_name, 1, 0);

		yaffs_update_parent(old_dir);
		if (new_dir != old_dir)
			yaffs_update_parent(new_dir);

		return result;
	}
	return YAFFS_FAIL;
}

/*----------------------- Initialisation Scanning ---------------------- */

void yaffs_handle_shadowed_obj(struct yaffs_dev *dev, int obj_id,
			       int backward_scanning)
{
	struct yaffs_obj *obj;

	if (backward_scanning) {
		/* Handle YAFFS2 case (backward scanning)
		 * If the shadowed object exists then ignore.
		 */
		obj = yaffs_find_by_number(dev, obj_id);
		if (obj)
			return;
	}

	/* Let's create it (if it does not exist) assuming it is a file so that
	 * it can do shrinking etc.
	 * We put it in unlinked dir to be cleaned up after the scanning
	 */
	obj =
	    yaffs_find_or_create_by_number(dev, obj_id, YAFFS_OBJECT_TYPE_FILE);
	if (!obj)
		return;
	obj->is_shadowed = 1;
	yaffs_add_obj_to_dir(dev->unlinked_dir, obj);
	obj->variant.file_variant.shrink_size = 0;
	obj->valid = 1;		/* So that we don't read any other info. */
}

void yaffs_link_fixup(struct yaffs_dev *dev, struct list_head *hard_list)
{
	struct list_head *lh;
	struct list_head *save;
	struct yaffs_obj *hl;
	struct yaffs_obj *in;

	list_for_each_safe(lh, save, hard_list) {
		hl = list_entry(lh, struct yaffs_obj, hard_links);
		in = yaffs_find_by_number(dev,
					hl->variant.hardlink_variant.equiv_id);

		if (in) {
			/* Add the hardlink pointers */
			hl->variant.hardlink_variant.equiv_obj = in;
			list_add(&hl->hard_links, &in->hard_links);
		} else {
			/* Todo Need to report/handle this better.
			 * Got a problem... hardlink to a non-existant object
			 */
			hl->variant.hardlink_variant.equiv_obj = NULL;
			INIT_LIST_HEAD(&hl->hard_links);
		}
	}
}

static void yaffs_strip_deleted_objs(struct yaffs_dev *dev)
{
	/*
	 *  Sort out state of unlinked and deleted objects after scanning.
	 */
	struct list_head *i;
	struct list_head *n;
	struct yaffs_obj *l;

	if (dev->read_only)
		return;

	/* Soft delete all the unlinked files */
	list_for_each_safe(i, n,
			   &dev->unlinked_dir->variant.dir_variant.children) {
		l = list_entry(i, struct yaffs_obj, siblings);
		yaffs_del_obj(l);
	}

	list_for_each_safe(i, n, &dev->del_dir->variant.dir_variant.children) {
		l = list_entry(i, struct yaffs_obj, siblings);
		yaffs_del_obj(l);
	}
}

/*
 * This code iterates through all the objects making sure that they are rooted.
 * Any unrooted objects are re-rooted in lost+found.
 * An object needs to be in one of:
 * - Directly under deleted, unlinked
 * - Directly or indirectly under root.
 *
 * Note:
 *  This code assumes that we don't ever change the current relationships
 *  between directories:
 *   root_dir->parent == unlinked_dir->parent == del_dir->parent == NULL
 *   lost-n-found->parent == root_dir
 *
 * This fixes the problem where directories might have inadvertently been
 * deleted leaving the object "hanging" without being rooted in the
 * directory tree.
 */

static int yaffs_has_null_parent(struct yaffs_dev *dev, struct yaffs_obj *obj)
{
	return (obj == dev->del_dir ||
		obj == dev->unlinked_dir || obj == dev->root_dir);
}

static void yaffs_fix_hanging_objs(struct yaffs_dev *dev)
{
	struct yaffs_obj *obj;
	struct yaffs_obj *parent;
	int i;
	struct list_head *lh;
	struct list_head *n;
	int depth_limit;
	int hanging;

	if (dev->read_only)
		return;

	/* Iterate through the objects in each hash entry,
	 * looking at each object.
	 * Make sure it is rooted.
	 */

	for (i = 0; i < YAFFS_NOBJECT_BUCKETS; i++) {
		list_for_each_safe(lh, n, &dev->obj_bucket[i].list) {
			obj = list_entry(lh, struct yaffs_obj, hash_link);
			parent = obj->parent;

			if (yaffs_has_null_parent(dev, obj)) {
				/* These directories are not hanging */
				hanging = 0;
			} else if (!parent ||
				   parent->variant_type !=
				   YAFFS_OBJECT_TYPE_DIRECTORY) {
				hanging = 1;
			} else if (yaffs_has_null_parent(dev, parent)) {
				hanging = 0;
			} else {
				/*
				 * Need to follow the parent chain to
				 * see if it is hanging.
				 */
				hanging = 0;
				depth_limit = 100;

				while (parent != dev->root_dir &&
				       parent->parent &&
				       parent->parent->variant_type ==
				       YAFFS_OBJECT_TYPE_DIRECTORY &&
				       depth_limit > 0) {
					parent = parent->parent;
					depth_limit--;
				}
				if (parent != dev->root_dir)
					hanging = 1;
			}
			if (hanging) {
				yaffs_trace(YAFFS_TRACE_SCAN,
					"Hanging object %d moved to lost and found",
					obj->obj_id);
				yaffs_add_obj_to_dir(dev->lost_n_found, obj);
			}
		}
	}
}

/*
 * Delete directory contents for cleaning up lost and found.
 */
static void yaffs_del_dir_contents(struct yaffs_obj *dir)
{
	struct yaffs_obj *obj;
	struct list_head *lh;
	struct list_head *n;

	if (dir->variant_type != YAFFS_OBJECT_TYPE_DIRECTORY)
		BUG();

	list_for_each_safe(lh, n, &dir->variant.dir_variant.children) {
		obj = list_entry(lh, struct yaffs_obj, siblings);
		if (obj->variant_type == YAFFS_OBJECT_TYPE_DIRECTORY)
			yaffs_del_dir_contents(obj);
		yaffs_trace(YAFFS_TRACE_SCAN,
			"Deleting lost_found object %d",
			obj->obj_id);
		yaffs_unlink_obj(obj);
	}
}

static void yaffs_empty_l_n_f(struct yaffs_dev *dev)
{
	yaffs_del_dir_contents(dev->lost_n_found);
}


struct yaffs_obj *yaffs_find_by_name(struct yaffs_obj *directory,
				     const YCHAR *name)
{
	int sum;
	struct list_head *i;
	YCHAR buffer[YAFFS_MAX_NAME_LENGTH + 1];
	struct yaffs_obj *l;

	if (!name)
		return NULL;

	if (!directory) {
		yaffs_trace(YAFFS_TRACE_ALWAYS,
			"tragedy: yaffs_find_by_name: null pointer directory"
			);
		BUG();
		return NULL;
	}
	if (directory->variant_type != YAFFS_OBJECT_TYPE_DIRECTORY) {
		yaffs_trace(YAFFS_TRACE_ALWAYS,
			"tragedy: yaffs_find_by_name: non-directory"
			);
		BUG();
	}

	sum = yaffs_calc_name_sum(name);

	list_for_each(i, &directory->variant.dir_variant.children) {
		l = list_entry(i, struct yaffs_obj, siblings);

		if (l->parent != directory)
			BUG();

		yaffs_check_obj_details_loaded(l);

		/* Special case for lost-n-found */
		if (l->obj_id == YAFFS_OBJECTID_LOSTNFOUND) {
			if (!strcmp(name, YAFFS_LOSTNFOUND_NAME))
				return l;
		} else if (l->sum == sum || l->hdr_chunk <= 0) {
			/* LostnFound chunk called Objxxx
			 * Do a real check
			 */
			yaffs_get_obj_name(l, buffer,
				YAFFS_MAX_NAME_LENGTH + 1);
			if (!strncmp(name, buffer, YAFFS_MAX_NAME_LENGTH))
				return l;
		}
	}
	return NULL;
}

/* GetEquivalentObject dereferences any hard links to get to the
 * actual object.
 */

struct yaffs_obj *yaffs_get_equivalent_obj(struct yaffs_obj *obj)
{
	if (obj && obj->variant_type == YAFFS_OBJECT_TYPE_HARDLINK) {
		obj = obj->variant.hardlink_variant.equiv_obj;
		yaffs_check_obj_details_loaded(obj);
	}
	return obj;
}

/*
 *  A note or two on object names.
 *  * If the object name is missing, we then make one up in the form objnnn
 *
 *  * ASCII names are stored in the object header's name field from byte zero
 *  * Unicode names are historically stored starting from byte zero.
 *
 * Then there are automatic Unicode names...
 * The purpose of these is to save names in a way that can be read as
 * ASCII or Unicode names as appropriate, thus allowing a Unicode and ASCII
 * system to share files.
 *
 * These automatic unicode are stored slightly differently...
 *  - If the name can fit in the ASCII character space then they are saved as
 *    ascii names as per above.
 *  - If the name needs Unicode then the name is saved in Unicode
 *    starting at oh->name[1].

 */
static void yaffs_fix_null_name(struct yaffs_obj *obj, YCHAR *name,
				int buffer_size)
{
	/* Create an object name if we could not find one. */
	if (strnlen(name, buffer_size) == 0) {
		YCHAR local_name[20];
		YCHAR num_string[20];
		YCHAR *x = &num_string[19];
		unsigned v = obj->obj_id;
		num_string[19] = 0;
		while (v > 0) {
			x--;
			*x = '0' + (v % 10);
			v /= 10;
		}
		/* make up a name */
		strcpy(local_name, YAFFS_LOSTNFOUND_PREFIX);
		strcat(local_name, x);
		strncpy(name, local_name, buffer_size - 1);
	}
}

int yaffs_get_obj_name(struct yaffs_obj *obj, YCHAR *name, int buffer_size)
{
	memset(name, 0, buffer_size * sizeof(YCHAR));
	yaffs_check_obj_details_loaded(obj);
	if (obj->obj_id == YAFFS_OBJECTID_LOSTNFOUND) {
		strncpy(name, YAFFS_LOSTNFOUND_NAME, buffer_size - 1);
	} else if (obj->short_name[0]) {
		strcpy(name, obj->short_name);
	} else if (obj->hdr_chunk > 0) {
		int result;
		u8 *buffer = yaffs_get_temp_buffer(obj->my_dev);

		struct yaffs_obj_hdr *oh = (struct yaffs_obj_hdr *)buffer;

		memset(buffer, 0, obj->my_dev->data_bytes_per_chunk);

		if (obj->hdr_chunk > 0) {
			result = yaffs_rd_chunk_tags_nand(obj->my_dev,
				obj->hdr_chunk, buffer, NULL);
			if (result == YAFFS_OK)
				yaffs_load_name_from_oh(obj->my_dev, name,
					oh->name, buffer_size);
		}
		yaffs_release_temp_buffer(obj->my_dev, buffer);
	}

	yaffs_fix_null_name(obj, name, buffer_size);

	return strnlen(name, YAFFS_MAX_NAME_LENGTH);
}

loff_t yaffs_get_obj_length(struct yaffs_obj *obj)
{
	/* Dereference any hard linking */
	obj = yaffs_get_equivalent_obj(obj);

	if (obj->variant_type == YAFFS_OBJECT_TYPE_FILE)
		return obj->variant.file_variant.file_size;
	if (obj->variant_type == YAFFS_OBJECT_TYPE_SYMLINK) {
		if (!obj->variant.symlink_variant.alias)
			return 0;
		return strnlen(obj->variant.symlink_variant.alias,
				     YAFFS_MAX_ALIAS_LENGTH);
	} else {
		/* Only a directory should drop through to here */
		return obj->my_dev->data_bytes_per_chunk;
	}
}

int yaffs_get_obj_link_count(struct yaffs_obj *obj)
{
	int count = 0;
	struct list_head *i;

	if (!obj->unlinked)
		count++;	/* the object itself */

	list_for_each(i, &obj->hard_links)
	    count++;		/* add the hard links; */

	return count;
}

int yaffs_get_obj_inode(struct yaffs_obj *obj)
{
	obj = yaffs_get_equivalent_obj(obj);

	return obj->obj_id;
}

unsigned yaffs_get_obj_type(struct yaffs_obj *obj)
{
	obj = yaffs_get_equivalent_obj(obj);

	switch (obj->variant_type) {
	case YAFFS_OBJECT_TYPE_FILE:
		return DT_REG;
		break;
	case YAFFS_OBJECT_TYPE_DIRECTORY:
		return DT_DIR;
		break;
	case YAFFS_OBJECT_TYPE_SYMLINK:
		return DT_LNK;
		break;
	case YAFFS_OBJECT_TYPE_HARDLINK:
		return DT_REG;
		break;
	case YAFFS_OBJECT_TYPE_SPECIAL:
		if (S_ISFIFO(obj->yst_mode))
			return DT_FIFO;
		if (S_ISCHR(obj->yst_mode))
			return DT_CHR;
		if (S_ISBLK(obj->yst_mode))
			return DT_BLK;
		if (S_ISSOCK(obj->yst_mode))
			return DT_SOCK;
		return DT_REG;
		break;
	default:
		return DT_REG;
		break;
	}
}

YCHAR *yaffs_get_symlink_alias(struct yaffs_obj *obj)
{
	obj = yaffs_get_equivalent_obj(obj);
	if (obj->variant_type == YAFFS_OBJECT_TYPE_SYMLINK)
		return yaffs_clone_str(obj->variant.symlink_variant.alias);
	else
		return yaffs_clone_str(_Y(""));
}

/*--------------------------- Initialisation code -------------------------- */

static int yaffs_check_dev_fns(struct yaffs_dev *dev)
{
	struct yaffs_driver *drv = &dev->drv;
	struct yaffs_tags_handler *tagger = &dev->tagger;

	/* Common functions, gotta have */
	if (!drv->drv_read_chunk_fn ||
	    !drv->drv_write_chunk_fn ||
	    !drv->drv_erase_fn)
		return 0;

	if (dev->param.is_yaffs2 &&
	     (!drv->drv_mark_bad_fn  || !drv->drv_check_bad_fn))
		return 0;

	/* Install the default tags marshalling functions if needed. */
	yaffs_tags_compat_install(dev);
	yaffs_tags_marshall_install(dev);

	/* Check we now have the marshalling functions required. */
	if (!tagger->write_chunk_tags_fn ||
	    !tagger->read_chunk_tags_fn ||
	    !tagger->query_block_fn ||
	    !tagger->mark_bad_fn)
		return 0;

	return 1;
}

static int yaffs_create_initial_dir(struct yaffs_dev *dev)
{
	/* Initialise the unlinked, deleted, root and lost+found directories */
	dev->lost_n_found = NULL;
	dev->root_dir = NULL;
	dev->unlinked_dir = NULL;
	dev->del_dir = NULL;

	dev->unlinked_dir =
	    yaffs_create_fake_dir(dev, YAFFS_OBJECTID_UNLINKED, S_IFDIR);
	dev->del_dir =
	    yaffs_create_fake_dir(dev, YAFFS_OBJECTID_DELETED, S_IFDIR);
	dev->root_dir =
	    yaffs_create_fake_dir(dev, YAFFS_OBJECTID_ROOT,
				  YAFFS_ROOT_MODE | S_IFDIR);
	dev->lost_n_found =
	    yaffs_create_fake_dir(dev, YAFFS_OBJECTID_LOSTNFOUND,
				  YAFFS_LOSTNFOUND_MODE | S_IFDIR);

	if (dev->lost_n_found &&
		dev->root_dir &&
		dev->unlinked_dir &&
		dev->del_dir) {
			/* If lost-n-found is hidden then yank it out of the directory tree. */
			if (dev->param.hide_lost_n_found)
				list_del_init(&dev->lost_n_found->siblings);
			else
				yaffs_add_obj_to_dir(dev->root_dir, dev->lost_n_found);
		return YAFFS_OK;
	}
	return YAFFS_FAIL;
}

/* Low level init.
 * Typically only used by yaffs_guts_initialise, but also used by the
 * Low level yaffs driver tests.
 */

int yaffs_guts_ll_init(struct yaffs_dev *dev)
{


	yaffs_trace(YAFFS_TRACE_TRACING, "yaffs: yaffs_ll_init()");

	if (!dev) {
		yaffs_trace(YAFFS_TRACE_ALWAYS,
			"yaffs: Need a device"
			);
		return YAFFS_FAIL;
	}

	if (dev->ll_init)
		return YAFFS_OK;

	dev->internal_start_block = dev->param.start_block;
	dev->internal_end_block = dev->param.end_block;
	dev->block_offset = 0;
	dev->chunk_offset = 0;
	dev->n_free_chunks = 0;

	dev->gc_block = 0;

	if (dev->param.start_block == 0) {
		dev->internal_start_block = dev->param.start_block + 1;
		dev->internal_end_block = dev->param.end_block + 1;
		dev->block_offset = 1;
		dev->chunk_offset = dev->param.chunks_per_block;
	}

	/* Check geometry parameters. */

	if ((!dev->param.inband_tags && dev->param.is_yaffs2 &&
		dev->param.total_bytes_per_chunk < 1024) ||
		(!dev->param.is_yaffs2 &&
			dev->param.total_bytes_per_chunk < 512) ||
		(dev->param.inband_tags && !dev->param.is_yaffs2) ||
		 dev->param.chunks_per_block < 2 ||
		 dev->param.n_reserved_blocks < 2 ||
		dev->internal_start_block <= 0 ||
		dev->internal_end_block <= 0 ||
		dev->internal_end_block <=
		(dev->internal_start_block + dev->param.n_reserved_blocks + 2)
		) {
		/* otherwise it is too small */
		yaffs_trace(YAFFS_TRACE_ALWAYS,
			"NAND geometry problems: chunk size %d, type is yaffs%s, inband_tags %d ",
			dev->param.total_bytes_per_chunk,
			dev->param.is_yaffs2 ? "2" : "",
			dev->param.inband_tags);
		return YAFFS_FAIL;
	}

	/* Sort out space for inband tags, if required */
	if (dev->param.inband_tags)
		dev->data_bytes_per_chunk =
		    dev->param.total_bytes_per_chunk -
		    sizeof(struct yaffs_packed_tags2_tags_only);
	else
		dev->data_bytes_per_chunk = dev->param.total_bytes_per_chunk;

	/* Got the right mix of functions? */
	if (!yaffs_check_dev_fns(dev)) {
		/* Function missing */
		yaffs_trace(YAFFS_TRACE_ALWAYS,
			"device function(s) missing or wrong");

		return YAFFS_FAIL;
	}

	if (!yaffs_init_tmp_buffers(dev))
		return YAFFS_FAIL;

	if (yaffs_init_nand(dev) != YAFFS_OK) {
		yaffs_trace(YAFFS_TRACE_ALWAYS, "InitialiseNAND failed");
		return YAFFS_FAIL;
	}

	dev->ll_init = 1;

	return YAFFS_OK;
}


int yaffs_guts_format_dev(struct yaffs_dev *dev)
{
	u32 i;
	enum yaffs_block_state state;
	u32 dummy;

	if(yaffs_guts_ll_init(dev) != YAFFS_OK)
		return YAFFS_FAIL;

	if(dev->is_mounted)
		return YAFFS_FAIL;

	for (i = dev->internal_start_block; i <= dev->internal_end_block; i++) {
		yaffs_query_init_block_state(dev, i, &state, &dummy);
		if (state != YAFFS_BLOCK_STATE_DEAD)
			yaffs_erase_block(dev, i);
	}

	return YAFFS_OK;
}

/*
 * If the dev is mounted r/w then the cleanup will happen during
 * yaffs_guts_initialise. However if the dev is mounted ro then
 * the cleanup will be dfered until yaffs is remounted r/w.
 */
void yaffs_guts_cleanup(struct yaffs_dev *dev)
{
	yaffs_strip_deleted_objs(dev);
	yaffs_fix_hanging_objs(dev);
	if (dev->param.empty_lost_n_found)
			yaffs_empty_l_n_f(dev);
}

int yaffs_guts_initialise(struct yaffs_dev *dev)
{
	int init_failed = 0;
	u32 x;
	u32 bits;

	if(yaffs_guts_ll_init(dev) != YAFFS_OK)
		return YAFFS_FAIL;

	if (dev->is_mounted) {
		yaffs_trace(YAFFS_TRACE_ALWAYS, "device already mounted");
		return YAFFS_FAIL;
	}

	dev->is_mounted = 1;

	/* OK now calculate a few things for the device */

	/*
	 *  Calculate all the chunk size manipulation numbers:
	 */
	x = dev->data_bytes_per_chunk;
	/* We always use dev->chunk_shift and dev->chunk_div */
	dev->chunk_shift = calc_shifts(x);
	x >>= dev->chunk_shift;
	dev->chunk_div = x;
	/* We only use chunk mask if chunk_div is 1 */
	dev->chunk_mask = (1 << dev->chunk_shift) - 1;

	/*
	 * Calculate chunk_grp_bits.
	 * We need to find the next power of 2 > than internal_end_block
	 */

	x = dev->param.chunks_per_block * (dev->internal_end_block + 1);

	bits = calc_shifts_ceiling(x);

	/* Set up tnode width if wide tnodes are enabled. */
	if (!dev->param.wide_tnodes_disabled) {
		/* bits must be even so that we end up with 32-bit words */
		if (bits & 1)
			bits++;
		if (bits < 16)
			dev->tnode_width = 16;
		else
			dev->tnode_width = bits;
	} else {
		dev->tnode_width = 16;
	}

	dev->tnode_mask = (1 << dev->tnode_width) - 1;

	/* Level0 Tnodes are 16 bits or wider (if wide tnodes are enabled),
	 * so if the bitwidth of the
	 * chunk range we're using is greater than 16 we need
	 * to figure out chunk shift and chunk_grp_size
	 */

	if (bits <= dev->tnode_width)
		dev->chunk_grp_bits = 0;
	else
		dev->chunk_grp_bits = bits - dev->tnode_width;

	dev->tnode_size = (dev->tnode_width * YAFFS_NTNODES_LEVEL0) / 8;
	if (dev->tnode_size < sizeof(struct yaffs_tnode))
		dev->tnode_size = sizeof(struct yaffs_tnode);

	dev->chunk_grp_size = 1 << dev->chunk_grp_bits;

	if (dev->param.chunks_per_block < dev->chunk_grp_size) {
		/* We have a problem because the soft delete won't work if
		 * the chunk group size > chunks per block.
		 * This can be remedied by using larger "virtual blocks".
		 */
		yaffs_trace(YAFFS_TRACE_ALWAYS, "chunk group too large");

		return YAFFS_FAIL;
	}

	/* Finished verifying the device, continue with initialisation */

	/* More device initialisation */
	dev->all_gcs = 0;
	dev->passive_gc_count = 0;
	dev->oldest_dirty_gc_count = 0;
	dev->bg_gcs = 0;
	dev->gc_block_finder = 0;
	dev->buffered_block = -1;
	dev->doing_buffered_block_rewrite = 0;
	dev->n_deleted_files = 0;
	dev->n_bg_deletions = 0;
	dev->n_unlinked_files = 0;
	dev->n_ecc_fixed = 0;
	dev->n_ecc_unfixed = 0;
	dev->n_tags_ecc_fixed = 0;
	dev->n_tags_ecc_unfixed = 0;
	dev->n_erase_failures = 0;
	dev->n_erased_blocks = 0;
	dev->gc_disable = 0;
	dev->has_pending_prioritised_gc = 1; /* Assume the worst for now,
					      * will get fixed on first GC */
	INIT_LIST_HEAD(&dev->dirty_dirs);
	dev->oldest_dirty_seq = 0;
	dev->oldest_dirty_block = 0;

	yaffs_endian_config(dev);

	/* Initialise temporary caches. */
	dev->gc_cleanup_list = NULL;

	if (!init_failed)
		init_failed = yaffs_cache_init(dev) < 0;

	dev->cache_hits = 0;

	if (!init_failed) {
		dev->gc_cleanup_list =
		    kmalloc(dev->param.chunks_per_block * sizeof(u32),
					GFP_NOFS);
		if (!dev->gc_cleanup_list)
			init_failed = 1;
	}

	if (dev->param.is_yaffs2)
		dev->param.use_header_file_size = 1;

	if (!init_failed && !yaffs_init_blocks(dev))
		init_failed = 1;

	yaffs_init_tnodes_and_objs(dev);

	if (!init_failed && !yaffs_create_initial_dir(dev))
		init_failed = 1;

	if (!init_failed && dev->param.is_yaffs2 &&
		!dev->param.disable_summary &&
		!yaffs_summary_init(dev))
		init_failed = 1;

	if (!init_failed) {
		/* Now scan the flash. */
		if (dev->param.is_yaffs2) {
			if (yaffs2_checkpt_restore(dev)) {
				yaffs_check_obj_details_loaded(dev->root_dir);
				yaffs_trace(YAFFS_TRACE_CHECKPOINT |
					YAFFS_TRACE_MOUNT,
					"yaffs: restored from checkpoint"
					);
			} else {

				/* Clean up the mess caused by an aborted
				 * checkpoint load then scan backwards.
				 */
				yaffs_deinit_blocks(dev);

				yaffs_deinit_tnodes_and_objs(dev);

				dev->n_erased_blocks = 0;
				dev->n_free_chunks = 0;
				dev->alloc_block = -1;
				dev->alloc_page = -1;
				dev->n_deleted_files = 0;
				dev->n_unlinked_files = 0;
				dev->n_bg_deletions = 0;

				if (!init_failed && !yaffs_init_blocks(dev))
					init_failed = 1;

				yaffs_init_tnodes_and_objs(dev);

				if (!init_failed
				    && !yaffs_create_initial_dir(dev))
					init_failed = 1;

				if (!init_failed && !yaffs2_scan_backwards(dev))
					init_failed = 1;
			}
		} else if (!yaffs1_scan(dev)) {
			init_failed = 1;
		}

		yaffs_guts_cleanup(dev);
	}

	if (init_failed) {
		/* Clean up the mess */
		yaffs_trace(YAFFS_TRACE_TRACING,
		  "yaffs: yaffs_guts_initialise() aborted.");

		yaffs_deinitialise(dev);
		return YAFFS_FAIL;
	}

	/* Zero out stats */
	dev->n_page_reads = 0;
	dev->n_page_writes = 0;
	dev->n_erasures = 0;
	dev->n_gc_copies = 0;
	dev->n_retried_writes = 0;

	dev->n_retired_blocks = 0;

	yaffs_verify_free_chunks(dev);
	yaffs_verify_blocks(dev);

	/* Clean up any aborted checkpoint data */
	if (!dev->is_checkpointed && dev->blocks_in_checkpt > 0)
		yaffs2_checkpt_invalidate(dev);

	yaffs_trace(YAFFS_TRACE_TRACING,
	  "yaffs: yaffs_guts_initialise() done.");
	return YAFFS_OK;
}

void yaffs_deinitialise(struct yaffs_dev *dev)
{
	if (dev->is_mounted) {
		u32 i;

		yaffs_deinit_blocks(dev);
		yaffs_deinit_tnodes_and_objs(dev);
		yaffs_summary_deinit(dev);
		yaffs_cache_deinit(dev);

		kfree(dev->gc_cleanup_list);

		for (i = 0; i < YAFFS_N_TEMP_BUFFERS; i++) {
			kfree(dev->temp_buffer[i].buffer);
			dev->temp_buffer[i].buffer = NULL;
		}

		kfree(dev->checkpt_buffer);
		dev->checkpt_buffer = NULL;
		kfree(dev->checkpt_block_list);
		dev->checkpt_block_list = NULL;

		dev->ll_init = 0;
		dev->is_mounted = 0;

		yaffs_deinit_nand(dev);
	}
}

int yaffs_count_free_chunks(struct yaffs_dev *dev)
{
	int n_free = 0;
	u32 b;
	struct yaffs_block_info *blk;

	blk = dev->block_info;
	for (b = dev->internal_start_block; b <= dev->internal_end_block; b++) {
		switch (blk->block_state) {
		case YAFFS_BLOCK_STATE_EMPTY:
		case YAFFS_BLOCK_STATE_ALLOCATING:
		case YAFFS_BLOCK_STATE_COLLECTING:
		case YAFFS_BLOCK_STATE_FULL:
			n_free +=
			    (dev->param.chunks_per_block - blk->pages_in_use +
			     blk->soft_del_pages);
			break;
		default:
			break;
		}
		blk++;
	}
	return n_free;
}

int yaffs_get_n_free_chunks(struct yaffs_dev *dev)
{
	/* This is what we report to the outside world */
	int n_free;
	int n_dirty_caches;
	int blocks_for_checkpt;

	n_free = dev->n_free_chunks;
	n_free += dev->n_deleted_files;

	/* Now count and subtract the number of dirty chunks in the cache. */
	n_dirty_caches = yaffs_count_dirty_caches(dev);
	n_free -= n_dirty_caches;

	n_free -=
	    ((dev->param.n_reserved_blocks + 1) * dev->param.chunks_per_block);

	/* Now figure checkpoint space and report that... */
	blocks_for_checkpt = yaffs_calc_checkpt_blocks_required(dev);

	n_free -= (blocks_for_checkpt * dev->param.chunks_per_block);

	if (n_free < 0)
		n_free = 0;

	return n_free;
}

/*
 * Marshalling functions to get the appropriate time values saved
 * and restored to/from obj headers.
 *
 * Note that the WinCE time fields are used to store the 32-bit values.
 */

static void yaffs_oh_time_load(u32 *yst_time, u32 *win_time, YTIME_T timeval)
{
	u32 upper;
	u32 lower;

	lower = timeval & 0xffffffff;
	/* we have to use #defines here insted of an if statement
	otherwise the compiler throws an error saying that
	right shift count >= width of type when we are using 32 bit time.
	*/
	#ifdef CONFIG_YAFFS_USE_32_BIT_TIME_T
                upper = 0;
        #else
                upper = (timeval >> 32) & 0xffffffff;
        #endif

	*yst_time = lower;
	win_time[0] = lower;
	win_time[1] = upper;
}

static YTIME_T yaffs_oh_time_fetch(const u32 *yst_time, const u32 *win_time)
{
	u32 upper;
	u32 lower;

	if (win_time[1] == 0xffffffff) {
		upper = 0;
		lower = *yst_time;
	} else {
		upper = win_time[1];
		lower = win_time[0];
	}
	if (sizeof(YTIME_T) > sizeof(u32)) {
		u64 ret;
		ret = (((u64)upper) << 32) | lower;
		return (YTIME_T) ret;

	} else
		return (YTIME_T) lower;
}

YTIME_T yaffs_oh_ctime_fetch(struct yaffs_obj_hdr *oh)
{
	return yaffs_oh_time_fetch(&oh->yst_ctime, oh->win_ctime);
}

YTIME_T yaffs_oh_mtime_fetch(struct yaffs_obj_hdr *oh)
{
	return yaffs_oh_time_fetch(&oh->yst_mtime, oh->win_mtime);
}

YTIME_T yaffs_oh_atime_fetch(struct yaffs_obj_hdr *oh)
{
	return yaffs_oh_time_fetch(&oh->yst_atime, oh->win_atime);
}

void yaffs_oh_ctime_load(struct yaffs_obj *obj, struct yaffs_obj_hdr *oh)
{
	yaffs_oh_time_load(&oh->yst_ctime, oh->win_ctime, obj->yst_ctime);
}

void yaffs_oh_mtime_load(struct yaffs_obj *obj, struct yaffs_obj_hdr *oh)
{
	yaffs_oh_time_load(&oh->yst_mtime, oh->win_mtime, obj->yst_mtime);
}

void yaffs_oh_atime_load(struct yaffs_obj *obj, struct yaffs_obj_hdr *oh)
{
	yaffs_oh_time_load(&oh->yst_atime, oh->win_atime, obj->yst_atime);
}


/*
 * Marshalling functions to get loff_t file sizes into and out of
 * object headers.
 */
void yaffs_oh_size_load(struct yaffs_dev *dev,
			struct yaffs_obj_hdr *oh,
			loff_t fsize,
			int do_endian)
{
	oh->file_size_low = FSIZE_LOW(fsize);

	oh->file_size_high = FSIZE_HIGH(fsize);

	if (do_endian) {
		yaffs_do_endian_u32(dev, &oh->file_size_low);
		yaffs_do_endian_u32(dev, &oh->file_size_high);
	}
}

loff_t yaffs_oh_to_size(struct yaffs_dev *dev, struct yaffs_obj_hdr *oh,
			int do_endian)
{
	loff_t retval;


	if (sizeof(loff_t) >= 8 && ~(oh->file_size_high)) {
		u32 low = oh->file_size_low;
		u32 high = oh->file_size_high;

		if (do_endian) {
			yaffs_do_endian_u32 (dev, &low);
			yaffs_do_endian_u32 (dev, &high);
		}
		retval = FSIZE_COMBINE(high, low);
	} else {
		u32 low = oh->file_size_low;

		if (do_endian)
			yaffs_do_endian_u32(dev, &low);
		retval = (loff_t)low;
	}

	return retval;
}


void yaffs_count_blocks_by_state(struct yaffs_dev *dev, int bs[10])
{
	u32 i;
	struct yaffs_block_info *bi;
	int s;

	for(i = 0; i < 10; i++)
		bs[i] = 0;

	for(i = dev->internal_start_block; i <= dev->internal_end_block; i++) {
		bi = yaffs_get_block_info(dev, i);
		s = bi->block_state;
		if(s > YAFFS_BLOCK_STATE_DEAD || s < YAFFS_BLOCK_STATE_UNKNOWN)
			bs[0]++;
		else
			bs[s]++;
	}
}
